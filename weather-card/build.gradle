plugins {
    id 'com.android.library'
}

android {
    namespace 'com.sgmw.card'
    compileSdk 34

    defaultConfig {
        minSdk 24

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    dataBinding {
        enabled = true
    }

    //多车型配置
    flavorDimensions "default"
    productFlavors {
        _F510C {
            buildConfigField("int", "car_model", '1')
        }
        _F710C {
            buildConfigField("int", "car_model", '2')
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            jniLibs.srcDirs = ["libs"]
            res.srcDirs = ['src/main/res']
        }
        _F510C {
            res.srcDirs = ['src/main/res_f510c']
        }
        _F710C {
            res.srcDirs = ['src/main/res_f710c']
        }
    }
}
apply from: "${rootDir}/upload.gradle"
dependencies {

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'com.sgmw.permissionclient:permissionclient:1.0.3@aar'
    // Gson
    implementation 'com.google.code.gson:gson:2.10'
    implementation 'com.sw.car:android.car:2.0.37-SNAPSHOT'
}
