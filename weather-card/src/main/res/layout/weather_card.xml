<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="time"
            type="com.sgmw.card.viewmodel.TimeViewModel" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/d520"
        android:id="@+id/con_root"
        android:background="@mipmap/weather_card_bg"
        android:layout_height="@dimen/d168">

        <!--天气定位权限未开启-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_no_permission"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_permission"
                android:layout_width="@dimen/d100"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginLeft="@dimen/d40"
                android:background="@mipmap/ic_no_permission"
                android:layout_height="@dimen/d100" />

            <TextView
                android:id="@+id/tv_no_permission"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/iv_permission"
                android:text="@string/weather_no_permission"
                android:textSize="24sp"
                style="@style/cn_regular"
                android:layout_marginLeft="@dimen/d40"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气正在加载中…-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_loading"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent">
            
            <ImageView
                android:id="@+id/iv_loading"
                android:layout_width="@dimen/d100"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginLeft="@dimen/d40"
                android:background="@mipmap/ic_weather_loading"
                android:layout_height="@dimen/d100" />

            <TextView
                android:id="@+id/tv_loading"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/iv_loading"
                android:text="@string/weather_loading"
                android:textSize="24sp"
                style="@style/cn_regular"
                android:layout_marginLeft="@dimen/d40"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气数据获取失败\n请重试-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_load_fail"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_load_fail"
                android:layout_width="@dimen/d100"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginLeft="@dimen/d40"
                android:background="@mipmap/ic_no_permission"
                android:layout_height="@dimen/d100" />

            <TextView
                android:id="@+id/tv_load_fail"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/iv_load_fail"
                android:text="@string/weather_load_fail"
                android:textSize="24sp"
                style="@style/cn_regular"
                android:layout_marginLeft="@dimen/d40"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气数据展示-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_weather"
            android:layout_width="match_parent"
            android:visibility="visible"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_month_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d40"
                android:layout_marginTop="@dimen/d19"
                android:letterSpacing="0.025"
                android:text='@{time.getMonth() + "月" +time.getDay() +"日 " + time.getWeek()}'
                style="@style/tv_month_day"
                android:textSize="@dimen/s24"
                android:visibility="gone"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_card_location"
                android:layout_width="@dimen/d40"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginLeft="@dimen/d31"
                android:layout_marginTop="@dimen/d16"
                android:src="@mipmap/ic_location"
                android:layout_height="@dimen/d40" />
            
            <TextView
                android:id="@+id/tv_tempe"
                android:layout_width="wrap_content"
                android:text="28"
                android:textSize="80sp"
                style="@style/ms_rg"
                app:layout_constraintLeft_toLeftOf="@+id/iv_card_location"
                android:layout_marginLeft="@dimen/d3"
                app:layout_constraintTop_toBottomOf="@+id/iv_card_location"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="@dimen/d4"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_location"
                android:layout_width="wrap_content"
                app:layout_constraintLeft_toRightOf="@+id/iv_card_location"
                app:layout_constraintTop_toTopOf="@+id/iv_card_location"
                app:layout_constraintBottom_toBottomOf="@+id/iv_card_location"
                android:layout_marginBottom="@dimen/d5"
                android:textSize="24sp"
                android:layout_marginLeft="@dimen/d8"
                android:text="柳州市"
                style="@style/tv_location"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_unit_du"
                android:layout_width="wrap_content"
                android:layout_height="60dp"
                android:layout_marginTop="26dp"
                android:gravity="top"
                android:text="°C"
                android:textSize="31sp"
                style="@style/ms_rg"
                app:layout_constraintLeft_toRightOf="@+id/tv_tempe"
                app:layout_constraintTop_toTopOf="@+id/tv_tempe" />
            
            <ImageView
                android:id="@+id/iv_weather"
                android:layout_width="@dimen/d68"
                app:layout_constraintLeft_toRightOf="@+id/tv_tempe"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginLeft="@dimen/d45"
                android:layout_marginBottom="@dimen/d17"
                android:layout_height="@dimen/d68" />

            <TextView
                android:id="@+id/tv_tempe_range"
                android:layout_width="wrap_content"
                app:layout_constraintTop_toTopOf="@+id/iv_card_location"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_marginTop="@dimen/d18"
                android:layout_marginRight="@dimen/d32"
                android:text="30℃ ~ 20℃"
                android:textSize="20sp"
                android:textColor="@color/temp_range"
                style="@style/ms_light"
                android:layout_height="wrap_content" />
            
            <TextView
                android:id="@+id/tv_weather"
                android:layout_width="wrap_content"
                app:layout_constraintRight_toRightOf="@+id/tv_tempe_range"
                app:layout_constraintTop_toBottomOf="@+id/tv_tempe_range"
                app:layout_constraintBottom_toTopOf="@+id/tv_desc"
                android:text="晴"
                android:textSize="20sp"
                style="@style/cn_regular"
                android:layout_height="wrap_content" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="wrap_content"
                app:layout_constraintRight_toRightOf="@+id/tv_tempe_range"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginBottom="@dimen/d21"
                android:text=""
                android:textSize="20sp"
                style="@style/cn_regular"
                android:layout_height="wrap_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>