package com.sgmw.card.manager;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;

import com.sgmw.card.util.LogUtils;

public class DataReceiveManager {

    private static final String TAG = DataReceiveManager.class.getSimpleName();

    private Context mContext;
    public static final String NOTIFY_WEATHER_STATUS = "com.sgmw.weather.change_status";
    private static final String WEATHER_STATUS_KEY = "weather_status";

    private DataReceiveManager() {}

    private static DataReceiveManager dataReceiveManager;

    public static DataReceiveManager getInstance() {
        if (dataReceiveManager == null) {
            dataReceiveManager = new DataReceiveManager();
        }
        return dataReceiveManager;
    }

    public void registerWeatherStatusChange(Context context, WeatherChange weatherChange) {
        if (mContext != null) {
            mContext.unregisterReceiver(weatherStatusChange);
        }
        this.mContext = context;
        this.weatherChange = weatherChange;
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(NOTIFY_WEATHER_STATUS);
        // 动态注册广播接收器，避免 Android 8.0+ 的限制
        mContext.registerReceiver(weatherStatusChange, intentFilter);
    }

    public interface WeatherChange {
        void onWeatherChange(Bundle bundle);
    }

    private WeatherChange weatherChange;

    private BroadcastReceiver weatherStatusChange = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            LogUtils.i(TAG, "weatherStatusChange onReceive");
            if (weatherChange != null) {
                weatherChange.onWeatherChange(intent.getExtras());
            }
        }
    };
}
