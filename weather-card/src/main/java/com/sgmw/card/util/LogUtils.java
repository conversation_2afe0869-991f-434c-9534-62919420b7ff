package com.sgmw.card.util;

import android.util.Log;

/**
 * 从systemUi 复制来的 日志管理打印
 *
 * <AUTHOR>
 */
public class LogUtils {
    private static final boolean DEBUGABLE = true;
    private final static String TAG = LogUtils.class.getSimpleName();

    public static void v(String tag, String msg){
        if(DEBUGABLE){
            Log.v(TAG + tag, msg);
        }
    }

    public static void d(String tag, String msg){
        if(DEBUGABLE){
            Log.d(TAG + tag, msg);
        }
    }

    public static void i(String tag, String msg){
        if(DEBUGABLE){
            Log.i(TAG + tag, msg);
        }
    }

    public static void w(String tag, String msg){
        if(DEBUGABLE){
            Log.w(TAG + tag, msg);
        }
    }

    public static void e(String tag, String msg){
        if(DEBUGABLE){
            Log.e(TAG + tag, msg);
        }
    }

    public static void e(String tag, Throwable throwable){
        if(DEBUGABLE){
            Log.e(TAG + tag,"", throwable);
        }
    }
}
