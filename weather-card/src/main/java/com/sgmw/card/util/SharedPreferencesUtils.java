package com.sgmw.card.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import com.sgmw.card.entity.CardData;

import java.util.ArrayList;
import java.util.List;

public class SharedPreferencesUtils {
    public static final String TAG = "SharedPreferencesUtils";
    private static final String PROJECTION = "CarDiagHmi";

    public static boolean getBoolean(Context context, String key, boolean defaultValue) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        return preferences.getBoolean(key, defaultValue);
    }

    public static void saveBoolean(Context context, String key, boolean value) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(key, value);
        commit(editor);
    }

    public static int getInt(Context context, String key, int defaultValue) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        return preferences.getInt(key, defaultValue);
    }

    public static void saveInt(Context context, String key, int value) {
        LogUtils.i(TAG, "key == " + key + " || value == " + value);
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(key, value);
        commit(editor);
    }

    public static String getString(Context context, String key, String defaultValue) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        return preferences.getString(key, defaultValue);
    }

    public static void saveString(Context context, String key, String value) {
//        LogUtils.i(TAG, "saveString key == " + key + " value == " + value);
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        commitInMain(editor);
    }

    public static float getFloat(Context context, String key, float defaultValue) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        return preferences.getFloat(key, defaultValue);
    }

    public static void saveFloat(Context context, String key, float value) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putFloat(key, value);
        commit(editor);
    }

    public static long getLong(Context context, String key, long defaultValue) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        return preferences.getLong(key, defaultValue);
    }

    public static void saveLong(Context context, String key, long value) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putLong(key, value);
        commit(editor);
    }

    public static <T> ArrayList<T> getArray(Context context, String key, Class<T> cls) {
        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);

        ArrayList<T> datalist = new ArrayList<T>();
        String strJson = preferences.getString(key, null);
        if (null == strJson) {
            return datalist;
        }
        Log.d(TAG, "getDataList, json:" + strJson);
        try {
            Gson gson = new Gson();
            JsonArray array = new JsonParser().parse(strJson).getAsJsonArray();
            for (JsonElement jsonElement : array) {
                datalist.add(gson.fromJson(jsonElement, cls));
            }
        } catch (Exception e) {
            Log.e(TAG, "Exception : " + e.getMessage());
        }
        return datalist;
    }

    public static <T> void saveArray(Context context, String key, List<T> datalist) {
        Gson gson = new Gson();
        String strJson = gson.toJson(datalist);

        SharedPreferences preferences = context.getSharedPreferences(PROJECTION, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, strJson);
        commit(editor);
    }

    public static void commit(final SharedPreferences.Editor editor) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                editor.commit();
            }
        }).start();
    }

    //if do commit() in single thread, when we read sp imediately, we cannot get right value.
    public static void commitInMain(final SharedPreferences.Editor editor) {
        editor.commit();
    }
}
