package com.sgmw.card.viewmodel;


import androidx.databinding.ObservableField;
import androidx.lifecycle.ViewModel;

import com.sgmw.card.util.LogUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;

public class TimeViewModel extends ViewModel {
    private String TAG = "TimeViewModel";

    ObservableField<String> month = new ObservableField<>(" ");
    ObservableField<String> week = new ObservableField<>(" ");
    ObservableField<String> day = new ObservableField<>(" ");

    public void updateTime() {
        LogUtils.i(TAG, "updateTime:");
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH) + 1;
        LogUtils.i(TAG, "updateTime: month = " + month);
        this.month.set(month + "");
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        LogUtils.i(TAG, "updateTime: dayOfMonth = " + dayOfMonth);
        this.day.set(dayOfMonth + "");
        LogUtils.i(TAG, "updateTime: Locale.getDefault() = " + Locale.getDefault());
        SimpleDateFormat sdf = new SimpleDateFormat("EEEE", Locale.CHINA);
        String weekDay = sdf.format(calendar.getTime());
        LogUtils.i(TAG, "updateTime: weekDay = " + weekDay);
        this.week.set(weekDay);
    }


    public ObservableField<String> getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month.set(month);
    }

    public ObservableField<String> getWeek() {
        return week;
    }

    public void setWeek(String week) {
        this.week.set(week);
    }

    public ObservableField<String> getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day.set(day);
    }

}
