package com.sgmw.card.view;

import android.Manifest;
import android.car.bus.SGMWBus;
import android.car.bus.SGMWBusEvent;
import android.car.bus.SGMWBusEventType;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;

import com.sgmw.card.R;
import com.sgmw.card.constants.Constants;
import com.sgmw.card.databinding.WeatherCardBinding;
import com.sgmw.card.entity.CardData;
import com.sgmw.card.manager.DataReceiveManager;
import com.sgmw.card.util.GsonUtil;
import com.sgmw.card.util.LogUtils;
import com.sgmw.card.util.SharedPreferencesUtils;
import com.sgmw.card.viewmodel.TimeViewModel;
import com.sgmw.permissionsdk.PermissionManager;

public class WeatherCardContainer extends ConstraintLayout implements View.OnClickListener {

    private final static String TAG = WeatherCardContainer.class.getSimpleName();
    private Context mContext;
    private WeatherCardBinding weatherCardBinding;
    public static final String NOTIFY_WEATHER_STATUS = "com.sgmw.weather.change_status";
    private static final String WEATHER_STATUS_KEY = "weather_status";
    private static final String WEATHER_CARD_DATA = "weather_data";
    private static final String WEATHER_PACK_NAME = "com.sgmw.lingos.weather";
    private TimeViewModel timeViewModel;
    private Handler handler = new Handler(Looper.getMainLooper());
    public static final String LAST_STATUS = "LAST_STATUS";
    public static final String CARD_DATA = "CARD_DATA";
    private static final String LINGOS_AGREEMENT = "lingos_agree_agreement";
    private CardData cardData;
    private ContentObserver mSettingsObserver;

    public WeatherCardContainer(@NonNull Context context) {
        super(context);
        init(context);
    }

    public WeatherCardContainer(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public WeatherCardContainer(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LogUtils.i(TAG, "init!");
        mContext = context;
        if (cardData == null) {
            String lastData = SharedPreferencesUtils.getString(mContext, CARD_DATA, null);
            cardData = GsonUtil.fromJson(lastData, CardData.class);
        }
        weatherCardBinding = DataBindingUtil.inflate(LayoutInflater.from(mContext), R.layout.weather_card, this, true);
        timeViewModel = new TimeViewModel();
        weatherCardBinding.setTime(timeViewModel);
        weatherCardBinding.conRoot.setOnClickListener(this);
//        registerWeatherStatusChange();
        DataReceiveManager.getInstance().registerWeatherStatusChange(context, new DataReceiveManager.WeatherChange() {
            @Override
            public void onWeatherChange(Bundle extras) {
                int weatherStatus = extras.getInt(WEATHER_STATUS_KEY, 0);
                changeStatus(weatherStatus);

                Log.i(TAG, "onWeatherChange: -------weatherStatus" + weatherStatus
                        + "  serializable = " + extras.getString(WEATHER_CARD_DATA)
                        + " SDK_INT = " + Build.VERSION.SDK_INT
                        + " TIRAMISU = " + Build.VERSION_CODES.TIRAMISU);
                //if (weatherStatus == 4 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (weatherStatus == 4) {
                    String serializable = extras.getString(WEATHER_CARD_DATA);
                    cardData = GsonUtil.fromJson(serializable, CardData.class);
                    if (cardData != null) {
                        changeWeatherData(cardData);
                    }
                    if (timeViewModel != null) {
                        timeViewModel.updateTime();
                    }
                }
            }
        });
        requestWeather();
    }

    private void requestWeather() {
        Intent intent = new Intent("com.sgmw.weather.request_weather");
        LogUtils.i(TAG, "requestWeather == ");
        // 使用显式广播，避免 Android 8.0+ 的限制
        intent.setPackage("com.sgmw.lingos.weather");
        intent.putExtra("key", "weatherCardContainer");
        intent.putExtra("app", mContext.getPackageName());
        mContext.sendBroadcast(intent);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        LogUtils.i(TAG, "onAttachedToWindow" + WeatherCardContainer.this);
        if (timeViewModel != null) {
            LogUtils.i(TAG, "updateTime----------");
            timeViewModel.updateTime();
        }
        checkPermission();
//        mSettingsObserver= new ContentObserver(new Handler(Looper.getMainLooper())) {
//            @Override
//            public void onChange(boolean selfChange, @Nullable Uri uri) {
//                super.onChange(selfChange, uri);
//                String agreement = Settings.Global.getString(mContext.getContentResolver(), LINGOS_AGREEMENT);
//                LogUtils.i(TAG, "LINGOS_AGREEMENT agreement = " + agreement);
//                //同意 1 ; 其他值不同意
//                if (TextUtils.equals(agreement, "1")) {
////                    openWeatherApp();
//                }
//            }
//        };
//        mContext.getContentResolver().registerContentObserver(Settings.Global.getUriFor(LINGOS_AGREEMENT), false, mSettingsObserver);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        LogUtils.i(TAG, "onDetachedFromWindow" + WeatherCardContainer.this);
//        if (mSettingsObserver!=null){
//            mContext.getContentResolver().unregisterContentObserver(mSettingsObserver);
//        }

    }

    private void checkPermission() {
        boolean hasPermission = false;
        try {
            hasPermission = PermissionManager.getInstance().checkPermission(WEATHER_PACK_NAME, Manifest.permission_group.LOCATION);
            LogUtils.i(TAG, "hasPermission:" + hasPermission);
        } catch (RemoteException e) {
            LogUtils.e(TAG, e);
        }
        if (!hasPermission) {
            changeStatus(1);
            refreshUi(null);
        } else {
            if (cardData == null) {
                String lastData = SharedPreferencesUtils.getString(mContext, CARD_DATA, null);
                cardData = GsonUtil.fromJson(lastData, CardData.class);
            }
            refreshUi(cardData);
            if (cardData != null) {
                changeWeatherData(cardData);
            }
            changeStatus(SharedPreferencesUtils.getInt(mContext, LAST_STATUS, 3));
        }
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        int nightModeFlags = getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        LogUtils.i(TAG, "onConfigurationChanged current ui mode is :" + nightModeFlags);
//        if (cardData == null) {
//            String lastData = SharedPreferencesUtils.getString(mContext, CARD_DATA, null);
//            cardData = GsonUtil.fromJson(lastData, CardData.class);
//        }
//        refreshUi(cardData);
    }

    /**
     * 1 没权限
     * 2 加载中
     * 3 加载失败
     * 4 显示天气信息
     */
    private void changeStatus(int status) {
        LogUtils.i(TAG, "changeStatus----------" + status);
        LogUtils.i(TAG, "lastStatus----------" + SharedPreferencesUtils.getInt(mContext, LAST_STATUS, 3));
        //卡片实行静默加载（在有缓存的情况下不显示加载中状态）
        if (SharedPreferencesUtils.getInt(mContext, LAST_STATUS, 3) == 4 && status == 2) {
            return;
        }
        SharedPreferencesUtils.saveInt(mContext, LAST_STATUS, status);
        handler.post(() -> {
            weatherCardBinding.conNoPermission.setVisibility(View.GONE);
            weatherCardBinding.conLoading.setVisibility(View.GONE);
            weatherCardBinding.conLoadFail.setVisibility(View.GONE);
            weatherCardBinding.conWeather.setVisibility(View.GONE);
            switch (status) {
                case 1:
                    weatherCardBinding.conNoPermission.setVisibility(View.VISIBLE);
                    break;
                case 2:
                    weatherCardBinding.conLoading.setVisibility(View.VISIBLE);
                    break;
                case 3:
                    weatherCardBinding.conLoadFail.setVisibility(View.VISIBLE);
                    break;
                case 4:
                    weatherCardBinding.conWeather.setVisibility(View.VISIBLE);
                    break;
            }
        });
    }

    private void changeWeatherData(CardData cardData) {
        handler.post(() -> {
            SharedPreferencesUtils.saveString(mContext, CARD_DATA, GsonUtil.toJson(cardData));
            weatherCardBinding.tvLocation.setText(cardData.getLocation());
            weatherCardBinding.tvTempe.setText(cardData.getTemp());
            weatherCardBinding.tvTempeRange.setText(cardData.getMaxTemp() + " ~ " + cardData.getMinTemp());
            weatherCardBinding.tvWeather.setText(cardData.getWeather());
            weatherCardBinding.tvDesc.setText(cardData.getDesc());
            String weather = cardData.getWeather();
            LogUtils.i(TAG, cardData.getMoon() + "月" + cardData.getDay() + "日 星期" + cardData.getWeek());
            Integer resId = null;
            if (!cardData.isNight()) {
                resId = Constants.WEATHER_NAME_DES_ICON_DAY.get(weather);
                weatherCardBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_day);
            } else {
                resId = Constants.WEATHER_DES_ICON_NIGHT.get(weather);
                weatherCardBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_dark);
            }
        });
    }

    private void refreshUi(CardData cardData) {

        weatherCardBinding.conRoot.setBackground(mContext.getDrawable(R.drawable.weather_card_bg));
        weatherCardBinding.tvNoPermission.setTextColor(mContext.getColor(R.color.card_text));
        weatherCardBinding.ivPermission.setImageResource(R.mipmap.ic_no_permission);
        weatherCardBinding.tvLoading.setTextColor(mContext.getColor(R.color.card_text));
        weatherCardBinding.ivLoading.setImageResource(R.drawable.weather_animation_loading);
        weatherCardBinding.tvLoadFail.setTextColor(mContext.getColor(R.color.card_text));
        weatherCardBinding.ivLoadFail.setImageResource(R.mipmap.ic_no_permission);
        weatherCardBinding.ivCardLocation.setImageResource(R.mipmap.ic_location);
        weatherCardBinding.tvLocation.setTextColor(mContext.getColor(R.color.location_text));
        weatherCardBinding.tvTempe.setTextColor(mContext.getColor(R.color.temp));
        weatherCardBinding.tvUnitDu.setTextColor(mContext.getColor(R.color.temp));
        weatherCardBinding.tvTempeRange.setTextColor(mContext.getColor(R.color.temp_range));
        weatherCardBinding.tvWeather.setTextColor(mContext.getColor(R.color.card_text));
        weatherCardBinding.tvDesc.setTextColor(mContext.getColor(R.color.card_text));
        weatherCardBinding.tvMonthDay.setTextColor(mContext.getColor(R.color.card_text));
        if (cardData == null) {
            return;
        }
        String weather = cardData.getWeather();
        Integer resId = null;
        if (!cardData.isNight()) {
            resId = Constants.WEATHER_NAME_DES_ICON_DAY.get(weather);
            weatherCardBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_day);
        } else {
            resId = Constants.WEATHER_DES_ICON_NIGHT.get(weather);
            weatherCardBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_dark);
        }
    }

    @Override
    public void onClick(View v) {
        if (v == weatherCardBinding.conRoot) {
            registerLingOSAgreement();
        }
    }
    private void  registerLingOSAgreement(){
        int i=Settings.Global.getInt(mContext.getContentResolver(), LINGOS_AGREEMENT,0);
        if (i==1){
            openWeatherApp();
        }else {
            showWelcomeDialog();
        }
        LogUtils.i(TAG, "registerLingOSAgreement-----------");
    }
    //展示弹窗
    public void showWelcomeDialog() {
        SGMWBus sGMWBus = new SGMWBus(mContext);
        SGMWBusEvent sgmwBusEvent = new SGMWBusEvent();
        sgmwBusEvent.mEventType = SGMWBusEventType.EVENT_LINGOS_SHOW;
        sGMWBus.publish(sgmwBusEvent);
    }
    private void openWeatherApp(){
        ComponentName componentName = new ComponentName("com.sgmw.lingos.weather", "com.sgmw.lingos.weather.MainActivity");
        Intent intent = new Intent();
        intent.setComponent(componentName);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        getContext().startActivity(intent);
    }

    public class TimebroadcastReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (timeViewModel != null) {
                LogUtils.i(TAG, "updateTime----------");
                timeViewModel.updateTime();
            }
        }
    }
}    
