package com.sgmw.card.constants;


import com.sgmw.card.R;

import java.util.HashMap;

public class Constants {


    //Condition ID和对标的对应关系
    public static final HashMap<String, Integer> WEATHER_DES_ICON = new HashMap<>();

    static {
        WEATHER_DES_ICON.put("晴", R.mipmap.ic_sunny);// 晴
        WEATHER_DES_ICON.put("大部晴朗", R.mipmap.ic_mostly_sunny);// 大部晴朗
        WEATHER_DES_ICON.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_DES_ICON.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_DES_ICON.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_DES_ICON.put("阵雨", R.mipmap.ic_showers);//阵雨
        WEATHER_DES_ICON.put("局部阵雨", R.mipmap.ic_scattered_showers);//局部阵雨
        WEATHER_DES_ICON.put("小阵雨", R.mipmap.ic_light_showers);//小阵雨
        WEATHER_DES_ICON.put("强阵雨", R.mipmap.ic_heavy_showers);//强阵雨
        WEATHER_DES_ICON.put("阵雪", R.mipmap.ic_snow_showers);//阵雪
        WEATHER_DES_ICON.put("小阵雪", R.mipmap.ic_light_snow_showers);//小阵雪
        WEATHER_DES_ICON.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_DES_ICON.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_DES_ICON.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_DES_ICON.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_DES_ICON.put("尘卷风", R.mipmap.ic_tornado);//尘卷风
        WEATHER_DES_ICON.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_DES_ICON.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_DES_ICON.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_DES_ICON.put("雷阵雨", R.mipmap.ic_thundershower);//雷阵雨
        WEATHER_DES_ICON.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_DES_ICON.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_DES_ICON.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail);//雷阵雨伴有冰雹
        WEATHER_DES_ICON.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_DES_ICON.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_DES_ICON.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_DES_ICON.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_DES_ICON.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_DES_ICON.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_DES_ICON.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_DES_ICON.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_DES_ICON.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_DES_ICON.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_DES_ICON.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_DES_ICON.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_DES_ICON.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_DES_ICON.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_DES_ICON.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_DES_ICON.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_DES_ICON.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_DES_ICON.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_DES_ICON.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_DES_ICON.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    //日落Condition ID和对标的对应关系
    public static final HashMap<String, Integer> WEATHER_DES_ICON_NIGHT = new HashMap<>();

    static {
        WEATHER_DES_ICON_NIGHT.put("晴", R.mipmap.ic_sunny_dark);// 晴
        WEATHER_DES_ICON_NIGHT.put("大部晴朗", R.mipmap.ic_mostly_sunny_dark);// 大部晴朗
        WEATHER_DES_ICON_NIGHT.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_DES_ICON_NIGHT.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_DES_ICON_NIGHT.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_DES_ICON_NIGHT.put("阵雨", R.mipmap.ic_showers_dark);//阵雨
        WEATHER_DES_ICON_NIGHT.put("局部阵雨", R.mipmap.ic_scattered_showers_dark);//局部阵雨
        WEATHER_DES_ICON_NIGHT.put("小阵雨", R.mipmap.ic_light_showers_dark);//小阵雨
        WEATHER_DES_ICON_NIGHT.put("强阵雨", R.mipmap.ic_heavy_showers_dark);//强阵雨
        WEATHER_DES_ICON_NIGHT.put("阵雪", R.mipmap.ic_snow_showers_dark);//阵雪
        WEATHER_DES_ICON_NIGHT.put("小阵雪", R.mipmap.ic_light_snow_showers_dark);//小阵雪
        WEATHER_DES_ICON_NIGHT.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_DES_ICON_NIGHT.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_DES_ICON_NIGHT.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_DES_ICON_NIGHT.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_DES_ICON_NIGHT.put("尘卷风", R.mipmap.ic_tornado);//尘卷风
        WEATHER_DES_ICON_NIGHT.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_DES_ICON_NIGHT.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_DES_ICON_NIGHT.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_DES_ICON_NIGHT.put("雷阵雨", R.mipmap.ic_thundershower_dark);//雷阵雨
        WEATHER_DES_ICON_NIGHT.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_DES_ICON_NIGHT.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_DES_ICON_NIGHT.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_dark);//雷阵雨伴有冰雹
        WEATHER_DES_ICON_NIGHT.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_DES_ICON_NIGHT.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_DES_ICON_NIGHT.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_DES_ICON_NIGHT.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_DES_ICON_NIGHT.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_DES_ICON_NIGHT.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_DES_ICON_NIGHT.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_DES_ICON_NIGHT.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_DES_ICON_NIGHT.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_DES_ICON_NIGHT.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_DES_ICON_NIGHT.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_DES_ICON_NIGHT.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_DES_ICON_NIGHT.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_DES_ICON_NIGHT.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_DES_ICON_NIGHT.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_DES_ICON_NIGHT.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_DES_ICON_NIGHT.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_DES_ICON_NIGHT.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_DES_ICON_NIGHT.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_DES_ICON_NIGHT.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    //Condition name和天气icon的对应关系
    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON.put("晴", R.mipmap.ic_sunny);// 晴
        WEATHER_NAME_DES_ICON.put("大部晴朗", R.mipmap.ic_mostly_sunny);// 大部晴朗
        WEATHER_NAME_DES_ICON.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON.put("阵雨", R.mipmap.ic_showers);//阵雨
        WEATHER_NAME_DES_ICON.put("局部阵雨", R.mipmap.ic_scattered_showers);//局部阵雨
        WEATHER_NAME_DES_ICON.put("小阵雨", R.mipmap.ic_light_showers);//小阵雨
        WEATHER_NAME_DES_ICON.put("强阵雨", R.mipmap.ic_heavy_showers);//强阵雨
        WEATHER_NAME_DES_ICON.put("阵雪", R.mipmap.ic_snow_showers);//阵雪
        WEATHER_NAME_DES_ICON.put("小阵雪", R.mipmap.ic_light_snow_showers);//小阵雪
        WEATHER_NAME_DES_ICON.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON.put("雷阵雨", R.mipmap.ic_thundershower);//雷阵雨
        WEATHER_NAME_DES_ICON.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    //日落Condition name和天气icon的对应关系
    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON_NIGHT = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON_NIGHT.put("晴", R.mipmap.ic_sunny_dark);// 晴
        WEATHER_NAME_DES_ICON_NIGHT.put("大部晴朗", R.mipmap.ic_mostly_sunny_dark);// 大部晴朗
        WEATHER_NAME_DES_ICON_NIGHT.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON_NIGHT.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON_NIGHT.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON_NIGHT.put("阵雨", R.mipmap.ic_showers_dark);//阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("局部阵雨", R.mipmap.ic_scattered_showers_dark);//局部阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小阵雨", R.mipmap.ic_light_showers_dark);//小阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("强阵雨", R.mipmap.ic_heavy_showers_dark);//强阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("阵雪", R.mipmap.ic_snow_showers_dark);//阵雪
        WEATHER_NAME_DES_ICON_NIGHT.put("小阵雪", R.mipmap.ic_light_snow_showers_dark);//小阵雪
        WEATHER_NAME_DES_ICON_NIGHT.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON_NIGHT.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON_NIGHT.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON_NIGHT.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON_NIGHT.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON_NIGHT.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON_NIGHT.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON_NIGHT.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON_NIGHT.put("雷阵雨", R.mipmap.ic_thundershower_dark);//雷阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON_NIGHT.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON_NIGHT.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_dark);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON_NIGHT.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON_NIGHT.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON_NIGHT.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON_NIGHT.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON_NIGHT.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON_NIGHT.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON_NIGHT.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON_NIGHT.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON_NIGHT.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON_NIGHT.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON_NIGHT.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON_NIGHT.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON_NIGHT.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON_DAY = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON_DAY.put("晴", R.mipmap.ic_sunny_day);// 晴
        WEATHER_NAME_DES_ICON_DAY.put("大部晴朗", R.mipmap.ic_mostly_sunny_day);// 大部晴朗
        WEATHER_NAME_DES_ICON_DAY.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON_DAY.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON_DAY.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON_DAY.put("阵雨", R.mipmap.ic_showers_day);//阵雨
        WEATHER_NAME_DES_ICON_DAY.put("局部阵雨", R.mipmap.ic_scattered_showers_day);//局部阵雨
        WEATHER_NAME_DES_ICON_DAY.put("小阵雨", R.mipmap.ic_light_showers_day);//小阵雨
        WEATHER_NAME_DES_ICON_DAY.put("强阵雨", R.mipmap.ic_heavy_showers_day);//强阵雨
        WEATHER_NAME_DES_ICON_DAY.put("阵雪", R.mipmap.ic_snow_showers_day);//阵雪
        WEATHER_NAME_DES_ICON_DAY.put("小阵雪", R.mipmap.ic_light_snow_showers_day);//小阵雪
        WEATHER_NAME_DES_ICON_DAY.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON_DAY.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON_DAY.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON_DAY.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON_DAY.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON_DAY.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON_DAY.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON_DAY.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON_DAY.put("雷阵雨", R.mipmap.ic_thundershower_day);//雷阵雨
        WEATHER_NAME_DES_ICON_DAY.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON_DAY.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON_DAY.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_day);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON_DAY.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON_DAY.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON_DAY.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON_DAY.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON_DAY.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON_DAY.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON_DAY.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON_DAY.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON_DAY.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON_DAY.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON_DAY.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON_DAY.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON_DAY.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON_DAY.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON_DAY.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON_DAY.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON_DAY.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON_DAY.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON_DAY.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON_DAY.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON_DAY.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    public static final HashMap<String, Integer> DESC2CODE = new HashMap<>();

    static {
        DESC2CODE.put("晴", 1);// 晴
        DESC2CODE.put("大部晴朗", 6);// 大部晴朗
        DESC2CODE.put("多云", 8);// 多云

        DESC2CODE.put("少云", 12);//少云
        DESC2CODE.put("阴", 13);//阴
        DESC2CODE.put("阵雨", 15);//阵雨
        DESC2CODE.put("局部阵雨", 20);//局部阵雨

        DESC2CODE.put("小阵雨", 22);//小阵雨
        DESC2CODE.put("强阵雨", 23);//强阵雨
        DESC2CODE.put("阵雪", 24);//阵雪
        DESC2CODE.put("小阵雪", 25);//小阵雪
        DESC2CODE.put("雾", 26);//雾
        DESC2CODE.put("冻雾", 28);//冻雾
        DESC2CODE.put("沙尘暴", 29);//沙尘暴
        DESC2CODE.put("浮尘", 30);//浮尘
        DESC2CODE.put("尘卷风", 31);//尘卷风
        DESC2CODE.put("扬沙", 32);//扬沙
        DESC2CODE.put("强沙尘暴", 33);//强沙尘暴
        DESC2CODE.put("霾", 34);//霾
        DESC2CODE.put("阴", 36);//阴
        DESC2CODE.put("雷阵雨", 37);//雷阵雨
        DESC2CODE.put("雷电", 42);//雷电
        DESC2CODE.put("雷暴", 43);//雷暴
        DESC2CODE.put("雷阵雨伴有冰雹", 44);//雷阵雨伴有冰雹
        DESC2CODE.put("冰雹", 46);//冰雹
        DESC2CODE.put("冰针", 47);//冰针
        DESC2CODE.put("冰粒", 48);//冰粒
        DESC2CODE.put("雨夹雪", 49);//雨夹雪
        DESC2CODE.put("小雨", 51);//小雨
        DESC2CODE.put("中雨", 53);//中雨
        DESC2CODE.put("大雨", 54);//大雨
        DESC2CODE.put("暴雨", 55);//暴雨
        DESC2CODE.put("大暴雨", 56);//大暴雨
        DESC2CODE.put("特大暴雨", 57);//特大暴雨
        DESC2CODE.put("小雪", 58);//小雪
        DESC2CODE.put("中雪", 60);//中雪
        DESC2CODE.put("大雪", 62);//大雪
        DESC2CODE.put("暴雪", 63);//暴雪


        DESC2CODE.put("冻雨", 64);//冻雨
        DESC2CODE.put("小雨", 66);//小雨
        DESC2CODE.put("中雨", 67);//中雨
        DESC2CODE.put("大雨", 68);//大雨
        DESC2CODE.put("大暴雨", 69);//大暴雨
        DESC2CODE.put("小雪", 73);//小雪
        DESC2CODE.put("大雪", 76);//大雪
        DESC2CODE.put("雪", 77);//雪
        DESC2CODE.put("雨", 78);//雨
        DESC2CODE.put("霾", 79);//霾
        DESC2CODE.put("多云", 81);//多云
        DESC2CODE.put("雾", 84);//雾
        DESC2CODE.put("阴", 85);//阴
        DESC2CODE.put("阵雨", 86);//阵雨
        DESC2CODE.put("雷阵雨", 90);//雷阵雨
        DESC2CODE.put("小到中雨", 91);//小到中雨
        DESC2CODE.put("中到大雨", 92);//中到大雨
        DESC2CODE.put("大到暴雨", 93);//大到暴雨
        DESC2CODE.put("小到中雪", 94);//小到中雪
    }


}
