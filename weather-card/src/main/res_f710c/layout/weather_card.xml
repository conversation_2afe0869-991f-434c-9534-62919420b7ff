<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="time"
            type="com.sgmw.card.viewmodel.TimeViewModel" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/con_root"
        android:layout_width="@dimen/d480"
        android:layout_height="@dimen/d140"
        android:background="@drawable/weather_card_bg">

        <!--天气定位权限未开启-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_no_permission"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_permission"
                android:layout_width="@dimen/d144"
                android:layout_height="@dimen/d64"
                android:layout_marginLeft="@dimen/d54"
                android:background="@mipmap/ic_no_permission"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_no_permission"
                style="@style/cn_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d4"
                android:text="@string/weather_no_permission"
                android:textSize="20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_permission"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气正在加载中…-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_loading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_loading"
                android:layout_width="@dimen/d63"
                android:layout_height="@dimen/d63"
                android:layout_marginLeft="@dimen/d95"
                android:src="@drawable/weather_animation_loading"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_loading"
                style="@style/cn_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d44"
                android:text="@string/weather_loading"
                android:textSize="20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_loading"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气数据获取失败,请稍后重试-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_load_fail"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_load_fail"
                android:layout_width="@dimen/d144"
                android:layout_height="@dimen/d64"
                android:layout_marginLeft="@dimen/d19"
                android:background="@mipmap/ic_no_permission"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_load_fail"
                style="@style/cn_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d4"
                android:text="@string/weather_load_fail"
                android:textSize="20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/iv_load_fail"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气数据-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/con_weather"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <!--定位图标-->
            <ImageView
                android:id="@+id/iv_card_location"
                android:layout_width="@dimen/d40"
                android:layout_height="@dimen/d40"
                android:layout_marginLeft="@dimen/d19"
                android:layout_marginTop="@dimen/d15"
                android:src="@mipmap/ic_location"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--定位城市-->
            <TextView
                android:id="@+id/tv_location"
                style="@style/tv_location"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d3"
                android:text="北京市"
                android:textSize="20sp"
                app:layout_constraintStart_toEndOf="@+id/iv_card_location"
                app:layout_constraintTop_toTopOf="@+id/iv_card_location"
                app:layout_constraintBottom_toBottomOf="@+id/iv_card_location"/>

            <!--当前温度-->
            <TextView
                android:id="@+id/tv_tempe"
                style="@style/tv_tempe"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d31"
                android:text="28"
                android:textSize="50sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/iv_card_location" />

            <TextView
                android:id="@+id/tv_unit_du"
                style="@style/tv_unit_du"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d4"
                android:layout_marginTop="@dimen/d60"
                android:gravity="top"
                android:text="℃"
                android:textSize="20sp"
                app:layout_constraintLeft_toRightOf="@+id/tv_tempe"
                app:layout_constraintTop_toTopOf="parent" />

            <!--天气描述 例如晴 小雨...-->
            <TextView
                android:id="@+id/tv_weather"
                style="@style/tv_month_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d8"
                android:layout_marginTop="@dimen/d60"
                android:text="晴"
                android:textSize="20sp"
                app:layout_constraintStart_toEndOf="@+id/tv_unit_du"
                app:layout_constraintTop_toTopOf="parent" />

            <!--01月01日 星期一 -->
            <TextView
                android:id="@+id/tv_month_day"
                style="@style/tv_month_day"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/d10"
                android:layout_marginTop="@dimen/d19"
                android:letterSpacing="0.025"
                android:text='@{time.getMonth() + "月" +time.getDay() +"日 " + time.getWeek()}'
                android:textSize="20sp"
                app:layout_constraintLeft_toRightOf="@+id/tv_tempe"
                app:layout_constraintBottom_toBottomOf="@+id/tv_tempe" />

            <!--<TextView
                android:id="@+id/tv_unit_c"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/d23"
                android:layout_marginTop="@dimen/d8"
                android:layout_marginLeft="@dimen/d8"
                android:gravity="center_horizontal"
                android:text="C"
                android:textFontWeight="800"
                android:textColor="@color/text_symbol"
                android:textSize="17sp"
                app:layout_constraintLeft_toRightOf="@+id/tv_tempe"
                app:layout_constraintTop_toTopOf="@+id/tv_tempe" />-->

            <ImageView
                android:id="@+id/iv_weather"
                android:layout_width="@dimen/d116"
                android:layout_height="@dimen/d116"
                android:layout_marginRight="@dimen/d32"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_tempe_range"
                style="@style/ms_light"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d18"
                android:layout_marginRight="@dimen/d32"
                android:text="30℃ ~ 20℃"
                android:textColor="@color/temp_range"
                android:textSize="20sp"
                android:visibility="gone"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_location" />

            <TextView
                android:id="@+id/tv_desc"
                style="@style/cn_regular"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/d21"
                android:text=""
                android:textSize="20sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="@+id/tv_tempe_range" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>