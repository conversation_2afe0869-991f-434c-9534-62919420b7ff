<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="tv_month_day">
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">400</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/card_text</item>
    </style>

    <style name="tv_location">
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/location_text</item>
    </style>

    <style name="tv_tempe">
        <item name="fontFamily">sans-serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/temp</item>
    </style>

    <style name="tv_unit_du">
        <item name="fontFamily">sans-serif</item>
        <item name="android:textFontWeight">800</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/text_symbol</item>
    </style>
</resources>