plugins {
    id 'com.android.library'
}

android {
    compileSdk 34

    defaultConfig {
        minSdk 29
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    sourceSets {
        main {
            java {
                srcDirs 'src\\main\\java', 'src\\main\\java\\com\\sgmw', 'src\\main\\java\\com\\sgmw\\entity'
            }
        }
    }
}
apply from: "./upload.gradle"
dependencies {
}