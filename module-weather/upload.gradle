apply plugin: 'maven-publish'
//声明变量记录上传Maven库地址
def repositoryUrl
//判断发到正式库还是snapshot库
if (isReleaseBuild()) {
    //上传Release私有仓库
    repositoryUrl = 'http://syshome.autoai.com/artifactory/repository/sw-release/'
} else {
    //上传snapshot私有仓库，这个仓库可以同版本重复部署，通常用于开发调试版本
    repositoryUrl = 'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
}
def VERSION="1.0.1"

def isReleaseBuild() {
    return !VERSION.contains("SNAPSHOT-SNAPSHOT")
}

afterEvaluate {
    publishing {
        publications {
            if (isReleaseBuild()){
                release(MavenPublication) {
                    println("upload------------->release>>>>>" + repositoryUrl)
                    //from components.release
                    groupId 'com.sgmw.weather'
                    artifactId 'weather'
                    version VERSION
                    artifact("$buildDir/outputs/aar/${project.name}-release.aar")
                }
            }else {
                debug(MavenPublication) {
                    println("upload-------------debug>>>>>>" + repositoryUrl)
                    //from components.release
                    groupId 'com.sgmw.weather'
                    artifactId 'weather'
                    version VERSION
                    artifact("$buildDir/outputs/aar/${project.name}-debug.aar")
                }
            }


        }
        repositories {
//            mavenLocal()  // 发布到本地 Maven 仓库 (~/.m2/repository)
            // 或者发布到远程仓库
            maven {
                allowInsecureProtocol = true
                url = repositoryUrl
                credentials {
                    username = project.USERNAME
                    password = project.PASSWORD
                }
            }
        }
    }
}
