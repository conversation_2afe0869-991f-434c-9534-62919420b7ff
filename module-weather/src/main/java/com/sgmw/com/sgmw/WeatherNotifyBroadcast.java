package com.sgmw;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;


import java.util.List;

import com.sgmw.moduleweather.IWeatherInfoCallback;

public class WeatherNotifyBroadcast extends BroadcastReceiver {
    private String TAG = "WeatherNotifyBroadcast";
    private static final String NOTIFY_ACTIVITY = "com.sgmw.weather.refresh_weather";

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i(TAG, "10:onReceive:  start 收到天气变化");
        if (intent != null) {
            if (NOTIFY_ACTIVITY.equals(intent.getAction())) {
                String weatherCode = null;
                String weatherTemp = null;
                String sunUp = "";
                String sunDown = "";
                String location = "";
                Bundle extras = intent.getExtras();
                if (extras != null) {
                    weatherCode = extras.getString("weatherCode");
                    weatherTemp = extras.getString("weatherTemp");
                    //日出
                    sunUp = extras.getString("sunrise");
                    //日落
                    sunDown = extras.getString("sunset");
                    location = extras.getString("location");
                }

                if (extras == null || (TextUtils.isEmpty(weatherTemp) && TextUtils.isEmpty(weatherCode))) {
                    //失败
                    synchronized (WeatherControl.class) {
                        List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                        for (int i = 0; i < callbacks.size(); i++) {
                            IWeatherInfoCallback mCallBack = callbacks.get(i);
                            if (mCallBack != null) {
                                mCallBack.onGetWeatherDataFail();
                                Log.i(TAG, "34:onReceive: 失败回调 ");
                            }
                        }
                    }
                    return;
                }

                if (!TextUtils.isEmpty(weatherTemp)) {
                    synchronized (WeatherControl.class) {
                        List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                        for (int i = 0; i < callbacks.size(); i++) {
                            IWeatherInfoCallback mCallBack = callbacks.get(i);
                            if (mCallBack != null) {
                                mCallBack.onTempGetComplete(weatherTemp);
                                Log.i(TAG, "48:onReceive:  " + weatherTemp);
                            }
                        }
                    }
                }

                if (!TextUtils.isEmpty(weatherCode)) {
                    synchronized (WeatherControl.class) {
                        List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                        for (int i = 0; i < callbacks.size(); i++) {
                            IWeatherInfoCallback mCallBack = callbacks.get(i);
                            if (mCallBack != null) {
                                mCallBack.onWeatherGetComplete(weatherCode);
                                Log.i(TAG, "61:onReceive:  " + weatherCode);
                            }
                        }
                    }
                }

                if (!TextUtils.isEmpty(sunUp)) {
                    List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                    for (int i = 0; i < callbacks.size(); i++) {
                        IWeatherInfoCallback mCallBack = callbacks.get(i);
                        if (mCallBack != null) {
                            mCallBack.sunUp(sunUp);
                            Log.i(TAG, "99:onReceive:  "+sunUp);
                        }
                    }
                }

                if (!TextUtils.isEmpty(sunDown)) {
                    List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                    for (int i = 0; i < callbacks.size(); i++) {
                        IWeatherInfoCallback mCallBack = callbacks.get(i);
                        if (mCallBack != null) {
                            mCallBack.sunDown(sunDown);
                            Log.i(TAG, "110:onReceive:  "+sunDown);
                        }
                    }
                }
                if (!TextUtils.isEmpty(location)) {
                    List<IWeatherInfoCallback> callbacks = WeatherControl.getInstance(context).getCallbacks();
                    for (int i = 0; i < callbacks.size(); i++) {
                        IWeatherInfoCallback mCallBack = callbacks.get(i);
                        if (mCallBack != null) {
                            mCallBack.location(location);
                            Log.i(TAG, "110:onReceive:location   ");
                        }
                    }
                }
            }
        }
        Log.i(TAG, "68:onReceive: end ");
    }
}
