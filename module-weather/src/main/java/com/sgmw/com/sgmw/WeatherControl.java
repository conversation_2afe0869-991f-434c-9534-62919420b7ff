package com.sgmw;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

import com.sgmw.moduleweather.IWeatherInfoCallback;

public class WeatherControl {
    private String TAG = "WeatherControl";
    private static WeatherControl instance;
    private static List<IWeatherInfoCallback> callbacks = new ArrayList<>();

    private static Context mContext;
    private static final String GET_WEATHER = "com.sgmw.weather.request_weather";
    private static final String REFRESH_WEATHER = "com.sgmw.weather.refresh_weather";

    public static WeatherControl getInstance(Context context) {
        if (instance == null) {
            synchronized (WeatherControl.class) {
                if (instance == null) {
                    mContext = context.getApplicationContext();
                    instance = new WeatherControl();
                }
            }
        }
        return instance;
    }

    private WeatherControl() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(REFRESH_WEATHER);
        // 动态注册广播接收器，避免 Android 8.0+ 的限制
        mContext.registerReceiver(new WeatherNotifyBroadcast(), filter);
    }

    /**
     * 需要主动请求天气，天气才会回调
     * @param context
     */
    public void getWeather(Context context) {
        // TODO: 2024/3/25 通知天气获取天气
        Log.i(TAG, "40:getWeather:  获取广播");
        Intent intent = new Intent(GET_WEATHER);
        // 使用显式广播，避免 Android 8.0+ 的限制
        intent.setPackage("com.sgmw.lingos.weather");
        intent.putExtra("app", context.getPackageName());
        context.getApplicationContext().sendBroadcast(intent);
    }

    public void requestCallback(IWeatherInfoCallback callback) {
        synchronized (WeatherControl.class) {
            callbacks.add(callback);
        }
    }

    public void removeCallBack(IWeatherInfoCallback callback) {
        synchronized (WeatherControl.class) {
            if (callbacks.contains(callback)) {
                callbacks.remove(callback);
            }
        }
    }

    public List<IWeatherInfoCallback> getCallbacks() {
        return callbacks;
    }
}
