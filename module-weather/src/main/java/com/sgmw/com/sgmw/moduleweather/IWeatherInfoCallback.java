package com.sgmw.moduleweather;

public interface IWeatherInfoCallback {

    /**
     * 返回温度值
     */
    void onTempGetComplete(String temp);

    /**返回天气codeid，参照天气对照表进行设置
     *
     * @param weather
     */
    void onWeatherGetComplete(String weather);

    /**日升
     *
     * @param time
     */
    void sunUp(String time);

    /**日落
     *
     * @param time
     */
    void sunDown(String time);


    void onGetWeatherDataFail();

    /**当前位置
     *
     * @param location
     */
    void location(String location);

}
