com.sgmw.weather:weather:1.0.0@aar
以aar的形式 为导航、主题商店launcher 提供天气信息

1、注册监听
WeatherControl.getInstance().requestCallback(mWeatherInfoCallback);

2、需要主动请求天气，天气才会回调
WeatherControl.getInstance().getWeather()

3、重写监听回调方法，获取天气数据
private IWeatherInfoCallback mWeatherInfoCallback = new IWeatherInfoCallback() {
        @Override
        public void onTempGetComplete(String temp) {//返回温度值
            LogUtils.d("onTempGetComplete: " + temp);
        }

        @Override
        public void onWeatherGetComplete(String weather) {  //返回天气codeid，参照天气对照表进行设置
            LogUtils.d("onWeatherGetComplete: " + weather);
        }

        @Override
        public void onGetWeatherDataFail() {
        }

        @Override
        public void sunUp(String time){
            LogUtils.d("sunUp time: " + time);
        }

        @Override
        public void sunDown(String time){
            LogUtils.d("sunDown time: " + time);
        }

        @Override
        public void sunDown(String time){
            LogUtils.d("sunDown time: " + time);
        }

        @Override
        public void onGetWeatherDataFail(){
        }

        @Override
        public void location(String location){
        }

    };

4、移除监听
WeatherControl.getInstance().removeCallBack(mWeatherInfoCallback)