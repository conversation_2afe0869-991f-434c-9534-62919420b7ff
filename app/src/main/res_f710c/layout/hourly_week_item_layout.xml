<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/d160"
    android:layout_height="@dimen/d236"
    android:orientation="vertical">

    <TextView
        android:id="@+id/item_week_time_now"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:gravity="center"
        android:text="今天"
        android:textColor="@color/weather_text_color"
        android:textSize="@dimen/s25"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_week_day_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:gravity="center"
        android:text="11/28"
        android:textColor="@color/weather_text_color"
        android:textSize="@dimen/s25"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/item_week_day_icon"
        android:layout_width="@dimen/d72"
        android:layout_height="@dimen/d72"
        android:layout_marginTop="@dimen/d27"
        android:src="@mipmap/ic_fog"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/item_week_day_time" />

    <TextView
        android:id="@+id/item_week_day_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/d2"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:text="多云"
        android:textColor="@color/weather_text_color_70"
        android:textSize="20sp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/item_week_day_icon" />

    <TextView
        android:id="@+id/item_week_day_temp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/d14"
        android:layout_marginBottom="@dimen/d3"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:fontFamily="sans-serif-medium"
        android:text="19°C/39°C"
        android:textColor="@color/weather_text_color"
        android:textSize="@dimen/s28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>