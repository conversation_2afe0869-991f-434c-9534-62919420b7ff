<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.sgmw.lingos.weather.model.WeatherViewModel" />
    </data>

    <com.sgmw.lingos.weather.view.CustomNestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:overScrollMode="always">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_root"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/theme_bg">
            <!--无网络-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="88dp"
                android:orientation="vertical"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="MissingConstraints"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/iv_no_data"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/d377"
                    android:src="@mipmap/ic_nodata_400"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/d543"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    android:text="@string/def_net_err"
                    android:textColor="@color/weather_text_color"
                    android:textSize="@dimen/s28"
                    app:layout_constraintEnd_toEndOf="@id/iv_no_data"
                    app:layout_constraintStart_toStartOf="@id/iv_no_data"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_no_data"
                    android:layout_width="@dimen/d180"
                    android:layout_height="@dimen/d72"
                    android:layout_marginTop="@dimen/d631"
                    android:background="@drawable/no_network_retry_bg"
                    android:clickable="true"
                    android:fontFamily="serif"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:text="@string/def_reload"
                    android:textColor="@color/reload_text_color"
                    android:textFontWeight="600"
                    android:textSize="@dimen/s28"
                    app:layout_constraintEnd_toEndOf="@id/iv_no_data"
                    app:layout_constraintStart_toStartOf="@id/iv_no_data"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--加载中-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_loading_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/d88"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="MissingConstraints"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/iv_loading_data"
                    android:layout_width="@dimen/d156"
                    android:layout_height="@dimen/d156"
                    android:layout_marginTop="@dimen/d406"
                    android:src="@drawable/weather_animation_loading"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_loading_data"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/d24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    android:gravity="center"
                    android:text="@string/def_loading"
                    android:textSize="@dimen/s28"
                    app:layout_constraintLeft_toLeftOf="@+id/iv_loading_data"
                    app:layout_constraintRight_toRightOf="@+id/iv_loading_data"
                    app:layout_constraintTop_toBottomOf="@id/iv_loading_data" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--天气-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_page_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@mipmap/img_sunny"
                android:visibility="visible"
           >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_current_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d64"
                    android:layout_marginTop="@dimen/d115dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <ImageView
                        android:id="@+id/iv_location"
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_location"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:includeFontPadding="false"
                        android:id="@+id/tv_location"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d8"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_city_name"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s24"
                        app:layout_constraintBottom_toBottomOf="@+id/iv_location"
                        app:layout_constraintLeft_toRightOf="@+id/iv_location"
                        app:layout_constraintTop_toTopOf="@+id/iv_location" />

                    <TextView
                        android:includeFontPadding="false"
                        android:id="@+id/tv_temp_show"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/d180dp"
                        android:layout_marginTop="44dp"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:gravity="center"
                        android:text="@string/def_temperature"
                        android:textColor="@color/weather_text_color"
                        android:textSize="120sp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_unit_du"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/d25"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:fontFamily="sans-serif-medium"
                        android:includeFontPadding="false"
                        android:gravity="center"
                        android:text="@string/def_temp_unit"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s48"
                        app:layout_constraintLeft_toRightOf="@+id/tv_temp_show"
                        app:layout_constraintTop_toBottomOf="@+id/iv_location" />


                    <TextView
                        android:includeFontPadding="false"
                        android:id="@+id/tv_weather"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d40"
                        android:layout_marginBottom="@dimen/d78dp"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_weather"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s40"
                        app:layout_constraintLeft_toRightOf="@+id/tv_unit_du"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_temp_show"
                        />

                    <ImageView
                        android:id="@+id/iv_weather"
                        android:layout_width="@dimen/d72"
                        android:layout_height="@dimen/d72"
                        android:layout_marginStart="@dimen/d20"
                        android:src="@mipmap/ic_sunny"
                        android:layout_marginBottom="@dimen/d78dp"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_temp_show"
                        app:layout_constraintStart_toEndOf="@id/tv_weather"
                        app:layout_constraintTop_toTopOf="@+id/tv_weather" />

                    <TextView
                        android:id="@+id/tv_mintemp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_mintemp"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        android:layout_marginBottom="@dimen/d22dp"
                        app:layout_constraintStart_toStartOf="@+id/tv_weather"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_temp_show"  />

                    <TextView
                        android:id="@+id/tv_mintemp_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="°C"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_mintemp"
                        app:layout_constraintStart_toEndOf="@+id/tv_mintemp"
                        app:layout_constraintTop_toTopOf="@+id/tv_mintemp" />

                    <TextView
                        android:id="@+id/tv_temp_split"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_temp_split"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_mintemp"
                        app:layout_constraintStart_toEndOf="@+id/tv_mintemp_unit"
                        app:layout_constraintTop_toTopOf="@+id/tv_mintemp" />

                    <TextView
                        android:id="@+id/tv_maxtemp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_maxtemp"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_mintemp"
                        app:layout_constraintStart_toEndOf="@+id/tv_temp_split"
                        app:layout_constraintTop_toTopOf="@+id/tv_mintemp" />

                    <TextView
                        android:id="@+id/tv_maxtemp_unit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="°C"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintBottom_toBottomOf="@+id/tv_maxtemp"
                        app:layout_constraintStart_toEndOf="@+id/tv_maxtemp"
                        app:layout_constraintTop_toTopOf="@+id/tv_maxtemp" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_air_quality"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/d52"
                        android:background="@drawable/weather_quality_bg"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tv_temp_show">

                        <ImageView
                            android:id="@+id/iv_air_quality"
                            android:layout_width="@dimen/d48"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/d2"
                            android:src="@drawable/air_you_bg"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:layout_width="@dimen/d40"
                            android:layout_height="@dimen/d40"
                            android:layout_marginStart="@dimen/d6"
                            android:src="@mipmap/ic_air_quality"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_air_quality"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/d24dp"
                            android:contentDescription="(@hide_uc(value=[true]))"
                            android:text=""
                            android:paddingEnd="@dimen/d34"
                            android:textColor="@color/weather_text_color"
                            android:textSize="24sp"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/iv_air_quality"
                          />



                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <TextView
                        android:id="@+id/tv_evaluation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d24"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_evaluation"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s26"
                        android:includeFontPadding="false"
                        app:layout_constraintBottom_toBottomOf="@+id/cl_air_quality"
                        app:layout_constraintLeft_toRightOf="@+id/cl_air_quality"
                        app:layout_constraintTop_toTopOf="@+id/cl_air_quality" />
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/cl_air_live"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/d52"
                        android:layout_marginTop="@dimen/d36"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/cl_air_quality">

                        <ImageView
                            android:id="@+id/iv_air_wind"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/d2"
                            android:src="@mipmap/ic_wind"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_air_wind"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:contentDescription="(@hide_uc(value=[true]))"
                            android:hint="@string/def_text"
                            android:textColor="@color/weather_text_color"
                            android:textSize="24sp"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/iv_air_wind"
                            />
                        <ImageView
                            android:id="@+id/iv_air_wetness"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/d56dp"
                            android:src="@mipmap/ic_wetness"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/tv_air_wind"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_air_wetness"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:contentDescription="(@hide_uc(value=[true]))"
                            android:hint="@string/def_text"
                            android:textColor="@color/weather_text_color"
                            android:textSize="24sp"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/iv_air_wetness"
                            />
                        <ImageView
                            android:id="@+id/iv_air_feels"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/d56dp"
                            android:src="@mipmap/ic_feels"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/tv_air_wetness"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_air_feels"
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:contentDescription="(@hide_uc(value=[true]))"
                            android:hint="@string/def_text"
                            android:textColor="@color/weather_text_color"
                            android:textSize="24sp"
                            android:gravity="center"
                            android:includeFontPadding="false"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintLeft_toRightOf="@+id/iv_air_feels"
                            />
                    </androidx.constraintlayout.widget.ConstraintLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- 更新时间  刷新中 -->

                <!--24小时天气-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_refresh_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingEnd="@dimen/d64dp"
                    app:layout_constraintBottom_toBottomOf="@+id/ll_current_layout"
                    app:layout_constraintRight_toRightOf="parent">

                    <!-- 刷新按钮 -->

                    <ImageView
                        android:id="@+id/iv_refresh"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@mipmap/ic_loading"
                        app:layout_constraintEnd_toStartOf="@+id/tv_update_tip"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_update_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/d12"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:includeFontPadding="false"
                        android:text="@string/def_update_time"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s26"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/tv_update_time"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_update_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/def_update_clock"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s26"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 刷新中 -->
                    <TextView
                        android:id="@+id/tv_refresh"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/d12"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/def_update_time_animal"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s26"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="@+id/iv_refresh"
                        app:layout_constraintStart_toStartOf="@+id/tv_update_tip"
                        app:layout_constraintTop_toTopOf="@+id/iv_refresh" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_24h_weather"
                    android:layout_width="@dimen/d1012"
                    android:layout_height="@dimen/d400"
                    android:layout_marginStart="@dimen/d64"
                    android:layout_marginTop="@dimen/d12"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ll_current_layout">

                    <TextView
                        android:id="@+id/tv_24h_weather"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d46"
                        android:layout_marginTop="@dimen/d30"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_24h_weather"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/im_24h_weather_indicator"
                        android:layout_width="@dimen/d48"
                        android:layout_height="@dimen/d4"
                        android:layout_marginTop="@dimen/d6"
                        android:visibility="visible"
                        android:background="@mipmap/weather_indicator"
                        app:layout_constraintLeft_toLeftOf="@+id/tv_24h_weather"
                        app:layout_constraintRight_toRightOf="@+id/tv_24h_weather"
                        app:layout_constraintTop_toBottomOf="@+id/tv_24h_weather"/>

                    <TextView
                        android:id="@+id/tv_7day_weather"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d40"
                        android:layout_marginTop="@dimen/d30"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_7day_weather"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s24"
                        app:layout_constraintLeft_toRightOf="@+id/tv_24h_weather"
                        app:layout_constraintTop_toTopOf="parent" />

                    <View
                        android:id="@+id/im_7day_weather_indicator"
                        android:layout_width="@dimen/d48"
                        android:layout_height="@dimen/d4"
                        android:layout_marginTop="@dimen/d6"
                        android:background="@drawable/weather_indicator"
                        android:visibility="gone"
                        app:layout_constraintLeft_toLeftOf="@+id/tv_7day_weather"
                        app:layout_constraintRight_toRightOf="@+id/tv_7day_weather"
                        app:layout_constraintTop_toBottomOf="@+id/tv_7day_weather" />


                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_24h_weather"
                        android:layout_width="@dimen/d960"
                        android:layout_height="@dimen/d236"
                        android:layout_marginStart="@dimen/d26"
                        android:fadingEdgeLength="@dimen/d100"
                        android:orientation="horizontal"
                        android:requiresFadingEdge="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_24h_weather"
                        tools:ignore="MissingConstraints" />


                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_7day_weather_list"
                        android:layout_width="@dimen/d960"
                        android:layout_height="@dimen/d236"
                        android:layout_marginStart="@dimen/d26"
                        android:fadingEdgeLength="@dimen/d100"
                        android:orientation="horizontal"
                        android:requiresFadingEdge="horizontal"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_24h_weather"
                        tools:ignore="MissingConstraints" />


                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--指南-->
                <!--洗车-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_car_wash"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d40"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintLeft_toRightOf="@+id/cl_24h_weather"
                    app:layout_constraintTop_toTopOf="@+id/cl_24h_weather"
                    tools:ignore="MissingConstraints">

                    <ImageView
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp"
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_washcar"
                        />

                    <TextView
                        android:id="@+id/tv_car_wash"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_life_visibility_default"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_wash"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32"

                        />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--紫外线-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_ultraviolet_rays"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_car_wash"
                    app:layout_constraintLeft_toRightOf="@+id/cl_car_wash"
                    app:layout_constraintTop_toTopOf="@+id/cl_car_wash"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_uv"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp" />
                    <TextView
                        android:id="@+id/tv_ultraviolet_rays"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_uv"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32"/>


                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--感冒-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_common_cold"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_ultraviolet_rays"
                    app:layout_constraintLeft_toRightOf="@+id/cl_ultraviolet_rays"
                    app:layout_constraintTop_toTopOf="@+id/cl_ultraviolet_rays"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_ganmao"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp" />
                    <TextView
                        android:id="@+id/tv_common_cold"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_cold"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32" />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--空气污染-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_common_air"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_common_cold"
                    app:layout_constraintLeft_toRightOf="@+id/cl_common_cold"
                    app:layout_constraintTop_toTopOf="@+id/cl_common_cold"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_pollution"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp" />
                    <TextView
                        android:id="@+id/tv_common_air_quality"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_pollution"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--运动-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_sport"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintLeft_toLeftOf="@+id/cl_car_wash"
                    app:layout_constraintRight_toRightOf="@+id/cl_car_wash"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_24h_weather"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_sport"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp"
                        />


                    <TextView
                        android:id="@+id/tv_sport"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_sport"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32" />



                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--穿衣-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_dress"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_sport"
                    app:layout_constraintLeft_toRightOf="@+id/cl_sport"
                    app:layout_constraintTop_toTopOf="@+id/cl_sport"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_cloth"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp" />
                    <TextView
                        android:id="@+id/tv_dress"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_dress"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32" />

                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--旅游指数-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_travel_index"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_dress"
                    app:layout_constraintLeft_toRightOf="@+id/cl_dress"
                    app:layout_constraintTop_toTopOf="@+id/cl_dress"
                    tools:ignore="MissingConstraints">

                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:layout_marginStart="@dimen/d24dp"
                        android:layout_marginTop="@dimen/d16dp"
                        android:src="@mipmap/ic_travel"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                    <TextView
                        android:id="@+id/tv_travel_index"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d32"
                        android:layout_marginTop="@dimen/d79"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:fontFamily="sans-serif-medium"
                        android:text="@string/def_life_visibility_default"
                        android:textColor="@color/weather_text_color"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d32"
                        android:layout_marginTop="@dimen/d128dp"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_tourism"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--气压-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_pressure"
                    android:layout_width="@dimen/d165"
                    android:layout_height="@dimen/d185"
                    android:layout_marginStart="@dimen/d30"
                    android:background="@drawable/air_quality_bg"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_travel_index"
                    app:layout_constraintLeft_toRightOf="@+id/cl_travel_index"
                    app:layout_constraintTop_toTopOf="@+id/cl_travel_index"
                    tools:ignore="MissingConstraints">
                    <ImageView
                        android:layout_width="@dimen/d64"
                        android:layout_height="@dimen/d64"
                        android:src="@mipmap/ic_pressure"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d16dp"
                        android:layout_marginStart="@dimen/d24dp" />


                    <TextView
                        android:id="@+id/tv_pressure"
                        android:fontFamily="sans-serif-medium"
                        android:textColor="@color/weather_text_color"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_life_visibility_default"
                        android:textSize="@dimen/s32"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d79"
                        android:layout_marginStart="@dimen/d32" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:text="@string/def_cost_pressure"
                        android:textColor="@color/weather_text_color_70"
                        android:textSize="@dimen/s24"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="@dimen/d128dp"
                        android:layout_marginStart="@dimen/d32" />


                </androidx.constraintlayout.widget.ConstraintLayout>
                <!-- 备案号 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/d19"
                    android:paddingEnd="7dp"
                    app:layout_constraintRight_toRightOf="@+id/cl_pressure"
                    app:layout_constraintTop_toBottomOf="@+id/cl_pressure">

                    <TextView
                        android:id="@+id/tv_record_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="@string/weather_record_number"
                        android:textColor="@color/record_number_text_color"
                        android:textSize="20sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="MissingConstraints" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.sgmw.lingos.weather.view.CustomNestedScrollView>
</layout>