<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.sgmw.lingos.weather"
    android:sharedUserId="android.uid.system">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!--用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <!--用于访问GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!--用于获取运营商信息，用于支持提供运营商信息相关的接口-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!--用于访问wifi网络信息，wifi信息会用于进行网络定位-->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!--用于获取wifi的获取权限，wifi信息会用来进行网络定位-->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--用于访问网络，网络定位需要上网-->
    <uses-permission android:name="android.permission.INTERNET" />
    <!--用于读取手机当前的状态-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--用于写入缓存数据到扩展存储卡-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <!--用于申请调用A-GPS模块-->
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <!--如果设置了target >= 28 如果需要启动后台定位则必须声明这个权限-->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <!--如果您的应用需要后台定位权限，且有可能运行在Android Q设备上,并且设置了target>28，必须增加这个权限声明-->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!--用于监听重启-->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <!-- @SystemApi @hide Allows an application to start activities from background -->
    <permission android:name="android.permission.START_ACTIVITIES_FROM_BACKGROUND"
        tools:ignore="ReservedSystemPermission" />

    <application
        android:name=".WeatherApp"
        android:allowBackup="false"
        android:icon="@mipmap/ic_weather"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:persistent="true"
        android:roundIcon="@mipmap/ic_weather"
        android:supportsRtl="true"
        android:theme="@style/AppTheme">
        <activity
            android:name=".MainActivity"
            android:configChanges="uiMode|orientation|keyboard|keyboardHidden"
            android:exported="true"
            android:launchMode="singleInstance"
            android:screenOrientation="landscape"
            >
            <!-- 软键盘要顶起控件 -->
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".receiver.BootBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.RequestWeatherReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.sgmw.weather.request_weather"></action>
            </intent-filter>
        </receiver>
        <receiver
            android:name=".receiver.TimeChangeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.TIME_TICK"></action>
            </intent-filter>

        </receiver>
        <service android:name=".service.BootReceiverIntentService" android:exported="true" android:permission="android.permission.BIND_SERVICE"/>
    </application>

</manifest>