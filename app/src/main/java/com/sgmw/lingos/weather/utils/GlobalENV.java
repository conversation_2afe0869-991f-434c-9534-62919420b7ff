package com.sgmw.lingos.weather.utils;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import timber.log.Timber;

public class GlobalENV {
    private static final String logTag = LOG_TAG + "-GlobalENV:";
    //工程模式：后台环境
    private static final String ENVIRONMENT = "persist.vendor.sgmw.environment.testing";

    public static GlobalENV getInstance() {
        return Holder.instance;
    }

    public interface PowerWorkListener {
        void change(boolean status);
    }

    public PowerWorkListener powerWorkListener;

    private static class Holder {
        static GlobalENV instance = new GlobalENV();
    }

    public static boolean initApiMode(Context context) {
        //1为测试环境
        //0为正式环境
        boolean sDebugAPI = Settings.Global.getInt(context.getContentResolver(), ENVIRONMENT, 0) != 0;
        Timber.w(logTag + "initApiMode : sDebugAPI = %s", sDebugAPI);
        return sDebugAPI;
    }


    /**
     * 获取vin码
     */
    public static String getCarVin(){
        //测试数据
        String vinCode = SystemProperties.get("persist.vendor.sgmw.car.info.code");
        Log.i(logTag, "getCarVin  ===  " );
        return vinCode;
    }
}
