package com.sgmw.lingos.weather.utils;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import com.sgmw.entity.MapPositionData;
import com.sgmw.lingos.weather.WeatherApp;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import timber.log.Timber;

public class FilesUtil {
    private static final String logTag = LOG_TAG + "-FilesUtil:";
    public static MapPositionData getData() {
        MapPositionData mapPositionData = null;
        FileInputStream stream = null;
        try {
            String path = "/mapdata/svamapauto20/settingData/mapLocationData";
            File file = new File(path);
            if (file.isFile() && file.exists()) {
                stream = new FileInputStream(file);
                int size = stream.available();
                byte[] buffer = new byte[size];
                stream.read(buffer);
                String assetString = new String(buffer);
                WeatherApp instance = (WeatherApp) WeatherApp.getInstance();
                mapPositionData = instance.gson.fromJson(assetString, MapPositionData.class);
            } else {
                Timber.i(logTag+"FilesUtil:>>>>MapPositionData File No Exist");
            }
        } catch (Exception e) {
            Timber.i(logTag+"FilesUtil:>>>>MapPositionData Exception:" + e);
            //e.printStackTrace();
        } finally {
            safetyClose(stream);
        }
        if (mapPositionData == null) {
            Timber.i(logTag+"FilesUtil:>>>>MapPositionData mapPositionData==null");
        }
        return mapPositionData;
    }

    public static void safetyClose(FileInputStream closeable) {
        if (closeable != null) {
            try {
                closeable.close();
                Timber.i(logTag+"FilesUtil:>>>>FileInputStream Close Success");
            } catch (IOException e) {
                Timber.i(logTag+"FilesUtil:>>>>FileInputStream Close Fail");
                //e.printStackTrace();
            }
        } else {
            Timber.i(logTag+"FilesUtil:>>>>closeable==null");
        }
    }
}
