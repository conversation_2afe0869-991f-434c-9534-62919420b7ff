package com.sgmw.lingos.weather.model;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LIVE_INDEX;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_CITY;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_UPDATE_TIME;

import android.content.Context;
import android.os.RemoteException;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import com.sgmw.entity.LiveIndexExt;
import com.sgmw.entity.WeatherData;
import com.sgmw.lingos.weather.WeatherApp;
import com.sgmw.lingos.weather.helper.PrefUtils;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.server.ServiceConnector;
import com.sgmw.lingos.weather.utils.GsonUtil;
import com.sgmw.lingos.weather.utils.ThreadPoolUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import timber.log.Timber;

public class WeatherViewModel extends ViewModel {
    private static final String HOURS_12 = "12";
    private static final String HOURS_24 = "24";

    private static final String logTag = LOG_TAG + "-WeatherViewModel:";
    ServiceConnector connector;
    private MutableLiveData<String> currentCityName = new MutableLiveData<>();//获取定位城市
    private MutableLiveData<WeatherData> weatherData = new MutableLiveData<>();//获取天气信息
    private MutableLiveData<LiveIndexExt> liveIndexExt = new MutableLiveData<LiveIndexExt>();//获取生活指数
    private MutableLiveData<Boolean> isLoadingSuccess = new MutableLiveData<Boolean>(false);//获取生活指数
    private MutableLiveData<Boolean> isNight = new MutableLiveData<Boolean>();//获取当前时间是否日落
    public MutableLiveData<Boolean> mTimeLiveData;//获取时间显示格式 12小时制 24小时制
    public MutableLiveData<String> updateTimeLiveData = new MutableLiveData<>();//获取时间显示格式 12小时制 24小时制

    public WeatherViewModel() {
        connector = ServiceConnector.getInstance();
        currentCityName = connector.getCurrentCity();
        weatherData = connector.getWeatherData();
        updateTimeLiveData = connector.getUpdateTimeLiveData();
        liveIndexExt = connector.getLiveIndexExt();
        isLoadingSuccess = connector.getIsLoadingSuccess();
        isNight = connector.getIsNight();
        String timeStyle = Settings.System.getString(WeatherApp.getInstance().getContentResolver(), Settings.System.TIME_12_24);
        boolean is24Hours = HOURS_24.equals(timeStyle);
        mTimeLiveData = new MutableLiveData<>(is24Hours);
    }

    public MutableLiveData<String> getCurrentCityName() {
        return currentCityName;
    }

    public MutableLiveData<WeatherData> getWeatherData() {
        return weatherData;
    }

    public MutableLiveData<LiveIndexExt> getLiveIndex() {
        return liveIndexExt;
    }

    public MutableLiveData<Boolean> getIsLoadingSuccess() {
        return isLoadingSuccess;
    }

    public void setIsLoadingSuccess(boolean isLoadingSuccess) {
        this.isLoadingSuccess.setValue(isLoadingSuccess);
    }

    public MutableLiveData<Boolean> getIsNight() {
        return isNight;
    }

    public void setIsNight(MutableLiveData<Boolean> isNight) {
        this.isNight = isNight;
    }


    public MutableLiveData<Boolean> getTimeLevelData() {
        return mTimeLiveData;
    }

    public void setmTimeLiveData(MutableLiveData<Boolean> mTimeLiveData) {
        this.mTimeLiveData = mTimeLiveData;
    }

    public void reload(Context context) throws RemoteException {
        WeatherManger.getInstance(context).getNavAreaInfo();
    }


    public void refreshUpdateTime() {
        ThreadPoolUtils.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                if (PrefUtils.getInstance() != null) {
                    String updateTime = PrefUtils.getInstance().getString(SP_KEY_LAST_UPDATE_TIME);
                    Timber.i(logTag + "initView updateTime = " + updateTime);
                    if (!TextUtils.isEmpty(updateTime)) {
                        updateTimeLiveData.postValue(updateTime);
                    }
                }
            }
        });
    }

    public void refreshTimeLive(Context context) {
        ThreadPoolUtils.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                String timeStyle = Settings.System.getString(context.getApplicationContext().getContentResolver(), Settings.System.TIME_12_24);
                if (HOURS_12.equals(timeStyle)) {
                    mTimeLiveData.postValue(false);
                } else if (HOURS_24.equals(timeStyle)) {
                    mTimeLiveData.postValue(true);
                }
            }
        });
    }

    public void refreshCity() {
        ThreadPoolUtils.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                if (PrefUtils.getInstance() != null) {
                    String sLastLocationCity = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_CITY);
                    Timber.i(logTag + "initView sLastLocationCity = " + sLastLocationCity);
                    if (!TextUtils.isEmpty(sLastLocationCity)) {
                        currentCityName.postValue(sLastLocationCity);
                    }
                }
            }
        });
    }

    public void refreshLiveIndex() {
        ThreadPoolUtils.getInstance().execute(new Runnable() {
            @Override
            public void run() {
                if (PrefUtils.getInstance() != null) {
                    String liveIndex = PrefUtils.getInstance().getString(SP_KEY_LAST_LIVE_INDEX);
                    Timber.i(logTag + "initView liveIndex = " + liveIndex);
                    if (!TextUtils.isEmpty(liveIndex)) {
                        LiveIndexExt liveIndexExt1 = GsonUtil.fromJson(liveIndex, LiveIndexExt.class);
                        if (liveIndexExt1 != null) {
                            liveIndexExt.postValue(liveIndexExt1);
                        }
                    }
                }
            }
        });
    }


    /*
     * 格式化时间，获取当前时间小时
     *
     * */
    public int formatDate2Hour(Long date) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS", Locale.SIMPLIFIED_CHINESE);
        String d = format.format(date);
        Date date_tmp = format.parse(d);
        int serverHour = date_tmp.getHours();
        return serverHour;
    }


}
