package com.sgmw.lingos.weather.adapter;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class SpaceItemDecoration extends RecyclerView.ItemDecoration {

    private int mMargin;
    public SpaceItemDecoration(int margin){
        this.mMargin = margin;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        outRect.left = mMargin;
        if(parent.getChildAdapterPosition(view) == 0){
            outRect.left=0;
        }
    }
}
