package com.sgmw.lingos.weather.receiver;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import static com.sgmw.lingos.weather.utils.Constants.SETTING_UI_NIGHT_MODE;
import static com.sgmw.lingos.weather.utils.Constants.UI_AUTO_MODE;

import android.app.UiModeManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;

import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.utils.LogUtils;


public class BootBroadcastReceiver extends BroadcastReceiver {
    //重写onReceive方法
    private static final String logTag = LOG_TAG + "-BootBroadcastReceiver:";

    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtils.i(logTag, "SUN_TIME Boot onReceive");
        //启动应用，参数为需要自动启动的应用的包名
//        new Handler().postDelayed(() -> {
//            WeatherManger.getInstance(WeatherApp.getInstance());
//        },2000);
//        Intent service = new Intent(context, BootReceiverIntentService.class);
//        context.startService(service);

        //设置主题模式
        int[] sunTimes = WeatherManger.getInstance(context).getWeatherSunTime();
        int mThemeMode = WeatherManger.getInstance(context).getPresentThemeMode(sunTimes);
        UiModeManager mUiModeManager = (UiModeManager) context.getSystemService(Context.UI_MODE_SERVICE);
        int uiMode = mUiModeManager.getNightMode();

        int currentThemeMode = Settings.System.getInt(context.getContentResolver(), SETTING_UI_NIGHT_MODE, UI_AUTO_MODE);

        LogUtils.i(logTag, "SUN_TIME uiMode = " + currentThemeMode);
        LogUtils.i(logTag, "SUN_TIME currentThemeMode = " + currentThemeMode);

        LogUtils.i(logTag, "SUN_TIME 系统当前主题模式是否是自动 : " + (currentThemeMode == 2 ? "YES!!" : "NO!!!")
                + " 上次设置主题模式 = " + (uiMode == 1 ? "浅色" : "深色")
                + " 这次设置主题模式 = " + (mThemeMode == 1 ? "浅色" : "深色"));
        //当前是自动模式
        if (currentThemeMode == UI_AUTO_MODE) {
            //当前要设置的主题与系统不一致
            if (mThemeMode != uiMode) {
                mUiModeManager.setNightMode(mThemeMode);
                LogUtils.i(logTag, "SUN_TIME 设置主题模式" + (mThemeMode == 1 ? "浅色" : "深色"));
            }
        }
    }
}