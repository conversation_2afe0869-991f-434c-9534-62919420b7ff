package com.sgmw.lingos.weather.helper;

import static com.sgmw.lingos.weather.manager.WeatherManger.NAV_ERROR_CODE;
import static com.sgmw.lingos.weather.utils.Constants.DEFAULT_CITY_LAT;
import static com.sgmw.lingos.weather.utils.Constants.DEFAULT_CITY_LON;
import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LIVE_INDEX;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_CITY;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_LAT;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_LON;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_SUNRISE_INFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_SUNSET_INFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_WINFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_UPDATE_TIME;

import android.os.RemoteException;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.sgmw.entity.Forecast;
import com.sgmw.entity.Hourly;
import com.sgmw.entity.MapPositionData;
import com.sgmw.entity.ResponseWeatherBean;
import com.sgmw.entity.WeatherData;
import com.sgmw.lingos.weather.WeatherApp;
import com.sgmw.lingos.weather.callback.WeatherInfoCallback;
import com.sgmw.lingos.weather.entity.CardData;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.server.ServiceConnector;
import com.sgmw.lingos.weather.utils.Constants;
import com.sgmw.lingos.weather.utils.FilesUtil;
import com.sgmw.lingos.weather.utils.GCJ02_WGS84Utils;
import com.sgmw.lingos.weather.utils.GsonUtil;
import com.sgmw.lingos.weather.utils.LocateInfo;
import com.sgmw.lingos.weather.utils.LogUtils;
import com.sgmw.lingos.weather.utils.MyNetworkUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import timber.log.Timber;


/*
 * 封装与天气数据源的交互逻辑,通过天气 API 或访问天气服务器来获取天气数据
 * */


public class WeatherProvider {
    private static final String TAG = "WeatherProvider";
    private static final String logTag = LOG_TAG + "-WeatherProvider:";
    private String mLocationCity;//定位城市
    private String locationCityLat;//定位城市经度
    private String locationCityLon;//定位城市纬度
    private WeatherData weatherInfo;//天气信息
    private long wInfoUpTime;//更新天气时间，用于显示更新时间
    private int errorCode = -1;//错误代码

    private static final String HOURS_24 = "24";
    private static final String TIME_12 = "a hh:mm";
    private static final String TIME_24 = "HH:mm";

    private WeatherInfoCallback mWeatherInfoCallback;//天气信息接口回调，用于告知其它应用天气有更新
    private String requestCity = NAV_ERROR_CODE;


    public WeatherProvider() {
        //如果有缓存定位天气，那么优先显示上次的定位天气信息
        String sLastLocationLat = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_LAT);
        String sLastLocationLon = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_LON);
        if (!TextUtils.isEmpty(sLastLocationLat) && !TextUtils.isEmpty(sLastLocationLon)) {
            locationCityLat = sLastLocationLat;
            locationCityLon = sLastLocationLon;
            Timber.i(logTag + "Use cache locationCityLat =" + locationCityLat);
        } else {
            MapPositionData data = FilesUtil.getData();
            if (data != null && data.getLatitude() != null && !data.getLatitude().isEmpty() && data.getLongitude() != null && !data.getLongitude().isEmpty()) {
                try {
                    double latD = Double.parseDouble(data.getLatitude().trim());
                    double lonD = Double.parseDouble(data.getLongitude().trim());
                    LocateInfo locateInfo = GCJ02_WGS84Utils.gcj02_To_Wgs84(latD, lonD);
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LAT, locateInfo.getLatitude() + "");
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LON, locateInfo.getLongitude() + "");
                    Timber.i(logTag + "Use mapLocationData address locationCityLat="+ "locationCityLon=" );
                } catch (NumberFormatException e) {
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LAT, DEFAULT_CITY_LAT);
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LON, DEFAULT_CITY_LON);
                    LogUtils.e(TAG, e.getMessage());
                }
            } else {
                PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LAT, DEFAULT_CITY_LAT);
                PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_LON, DEFAULT_CITY_LON);
                Timber.i(logTag + "Use default lat lon =");
            }
        }
    }


    public void setmWeatherInfoCallback(WeatherInfoCallback mWeatherInfoCallback) {
        this.mWeatherInfoCallback = mWeatherInfoCallback;
    }

    public WeatherData getWeatherInfo() {
        return weatherInfo;
    }

    /*
     * 获取天气更新时间
     * */
    public long getwInfoUpTime() {
        return wInfoUpTime;
    }

    public String getLocationCityLat() {
        return locationCityLat;
    }

    public String getLocationCityLon() {
        return locationCityLon;
    }

    public String getmLocationCity() {
        return mLocationCity;
    }

    // 状态回调
    public interface WCallback {
        void onWeatherRetrieveReady(boolean success);
    }

    // 根据位置获取天气
    public void requestWeatherByPosition(String lat, String lon, final String name, String time, WCallback wCallback) {
        Timber.d(logTag + "requestWeatherByPosition >> %s, %s, %s", lat, lon, name);
        locationCityLat = lat;
        locationCityLon = lon;
        WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(2);
        NetHelper.getInstance().getWeatherByPosition(lat, lon, time, new Callback<ResponseWeatherBean>() {
            @Override
            public void onResponse(@NonNull Call<ResponseWeatherBean> call, @NonNull Response<ResponseWeatherBean> response) {
                Timber.i(logTag + "requestWeatherByPosition onResponse>>>>" + new Gson().toJson(response.body()));
                if (response.body() == null) {
                    if (wCallback != null) {
                        Timber.i(logTag + "requestWeatherByPosition response.body() == null");
                        wCallback.onWeatherRetrieveReady(false);
                        WeatherManger.getInstance(WeatherApp.getInstance()).setTemp2Appearance(WeatherManger.TEMP_ERROR_CODE);
                    }
                    return;
                }

                int code = response.body().getCode();
                long serverTimestamp = response.body().getCurrentTime();
                Timber.i(logTag + "Server date = %s", serverTimestamp);
                if (code == Constants.NET_REQUEST_SUCCESS) {
                    List<Hourly> hourlysFilter = new ArrayList<>();
                    if (serverTimestamp > 0 && response.body().getData() != null) {
                        WeatherData temp = response.body().getData();
                        List<Hourly> hourlys = new ArrayList<>(temp.getHourly());
                        Timber.i("%s hourlys size = %s", logTag, hourlys.size());
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS", Locale.SIMPLIFIED_CHINESE);
                        String d = format.format(serverTimestamp);
                        Timber.i(logTag + "Server date = %s", d);
                        try {
                            Date date = format.parse(d);
                            if (hourlys.size() > 0) {
                                int serverHour = date.getHours();
                                int nowIndex = -1;
                                for (int i = 0; i < hourlys.size(); i++) {
                                    Hourly hourly = hourlys.get(i);
                                    Timber.i("%s hourly getHour = %s", logTag, hourly.getHour());
                                    //2021-12-27 16:31:47
                                    if (Integer.parseInt(hourly.getHour()) == serverHour) {
                                        nowIndex = i;
                                        break;
                                    }
                                }
                                Timber.i("%s nowIndex = %s", logTag, nowIndex);
                                if (nowIndex != -1) {
                                    hourlysFilter = hourlys.subList(nowIndex, hourlys.size());
                                }
                            }
                        } catch (ParseException e) {
                            Timber.e("%sException:requestWeatherByPositionParseException", logTag);
                            //e.printStackTrace();
                        }
                    }

                    weatherInfo = response.body().getData();
                    Date currentDate = new Date();
                    String currentTime = String.format("%tF", currentDate);
                    if (hourlysFilter.size() > 0 && weatherInfo != null) {
                        Timber.i(logTag + "requestWeatherByPosition:use filter hourlySize=%s", hourlysFilter.size());
                        for (int i = 0; i < hourlysFilter.size(); i++) {
                            String date = hourlysFilter.get(i).getDate();
                            String hour = hourlysFilter.get(i).getHour();
                            Timber.i("%s  hourlysFilter[%d] date: %s,hour: %s", logTag, i, date, hour);
                            String sunset, sunrise;
                            List<Forecast> forecasts = weatherInfo.getForecast();
                            for (int j = 0; j < forecasts.size(); j++) {
                                String predictDate = forecasts.get(j).getPredictDate();
                                Timber.i("%s  forecasts[%d] predictDate: %s", logTag, j, predictDate);
                                if (date != null && predictDate != null) {
                                    if (date.equals(predictDate)) {
                                        //7天中每天的日出日落
                                        Forecast futureWeather = forecasts.get(j);
                                        sunrise = futureWeather.getSunrise();
                                        sunset = futureWeather.getSunset();
                                        Timber.i("%s  sunrise: %s , sunset: %s", logTag, sunrise, sunset);
                                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                        try {
                                            Date sunriseDateStr = format.parse(sunrise);
                                            Date sunsetDateStr = format.parse(sunset);
                                            int sunriseDate = sunriseDateStr.getHours();
                                            int sunsetDate = sunsetDateStr.getHours();
                                            int currentHours = Integer.parseInt(hour);
                                            hourlysFilter.get(i).setIconIsNight(currentHours <= sunriseDate || currentHours > sunsetDate);
                                            String futureTime = futureWeather.getPredictDate();
                                            if (TextUtils.equals(futureTime, currentTime)) {
                                                weatherInfo.setSunrise(sunriseDate);
                                                weatherInfo.setSunUp(sunriseDateStr.getTime());
                                                weatherInfo.setSunset(sunsetDate);
                                                weatherInfo.setSunDown(sunsetDateStr.getTime());
                                            }
                                            break;
                                        } catch (ParseException | NumberFormatException e) {
                                            e.printStackTrace();
                                        }

                                    }
                                }


                                if (j == 0) {
                                    //存储日升日落时间
                                    String mSunrise = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_SUNRISE_INFO);
                                    String newSunrise = forecasts.get(j).getSunrise();
                                    if (!TextUtils.equals(mSunrise, newSunrise)) {
                                        PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_SUNRISE_INFO, newSunrise);
                                        LogUtils.i(logTag, "SUN_TIME getWeatherByPosition onResponse：mSunrise = " + newSunrise);
                                    }
                                    String mSunset = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_SUNSET_INFO);
                                    String newSunset = forecasts.get(j).getSunset();
                                    if (!TextUtils.equals(mSunset, newSunset)) {
                                        PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_SUNSET_INFO, newSunset);
                                        LogUtils.i(logTag, "SUN_TIME getWeatherByPosition onResponse：Sunset = " + newSunrise);
                                    }
                                }
                            }
                        }
                        weatherInfo.setHourly(hourlysFilter);
                    }
                    weatherInfo.setCurrentTime(serverTimestamp);
                    //优化车机当前温度和墨迹当前温度不一致bug
                    setCurrentTemp(weatherInfo, "requestWeatherByPosition");
                    wInfoUpTime = System.currentTimeMillis();
                    Timber.i(logTag + "requestWeatherByPosition:cache weatherInfoLoc:%s", name);
                    PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_WINFO, new Gson().toJson(weatherInfo));
                    if (weatherInfo != null && weatherInfo.getDistrictName().startsWith(weatherInfo.getCityNameStr())) {
                        mLocationCity = weatherInfo.getDistrictName();
                    } else {
                        mLocationCity = weatherInfo == null ? "" : weatherInfo.getCityNameStr() + "-" + weatherInfo.getDistrictName();
                    }
                    if (NAV_ERROR_CODE.equals(requestCity)) {
                        //导航的接口返回失败的情况，然后天气接口返回了区县信息
                        if (mWeatherInfoCallback != null) {
                            mWeatherInfoCallback.onCityGetComplete(mLocationCity);
                            PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_CITY, mLocationCity);
                        }
                    }
                    Timber.i(logTag + "requestWeatherByPosition"+ weatherInfo.getCode());
                    if (weatherInfo != null) {
                        String json = GsonUtil.toJson(ServiceConnector.getInstance().createLiveIndexExt(weatherInfo.getLiveIndex()));
                        PrefUtils.getInstance().setString(SP_KEY_LAST_LIVE_INDEX, json);
                        // 格式化时间为时分
                        String timeStyle = Settings.System.getString(WeatherApp.getInstance().getContentResolver(), Settings.System.TIME_12_24);
                        String format = Boolean.TRUE.equals(HOURS_24.equals(timeStyle)) ? TIME_24 : TIME_12;
                        SimpleDateFormat sdf = new SimpleDateFormat(format);
                        String formattedTime = sdf.format(Calendar.getInstance().getTime());
                        PrefUtils.getInstance().setString(SP_KEY_LAST_UPDATE_TIME, formattedTime);
                    }
                    if (wCallback != null) {
                        Timber.i(logTag + "requestWeatherByPosition wCallback success");
                        wCallback.onWeatherRetrieveReady(true);
                    }
                    try {
                        if (mWeatherInfoCallback != null) {
                            if (weatherInfo == null) {
                                mWeatherInfoCallback.onWeatherGetComplete(null);
                                WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(3);
                            } else {
                                mWeatherInfoCallback.onWeatherGetComplete(weatherInfo.getCode() == null ? null : weatherInfo);
                                CardData cardData = WeatherManger.getInstance(WeatherApp.getInstance()).conver2CardData(weatherInfo);
                                WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(4, cardData);

                            }
                        }
                    } catch (RemoteException e) {
                        Timber.i(logTag + "requestWeatherByPosition wCallback fail");
                    }
                    // 定位成功，显示定位信息，切换到定位信息的开关
                    //isShowFav = false;

                } else {
                    if (wCallback != null) {
                        Timber.i(logTag + "requestWeatherByPosition wCallback false");
                        wCallback.onWeatherRetrieveReady(false);
                        weatherInfo = new WeatherData();
                        errorCode = code;
                    }
                    try {
                        if (mWeatherInfoCallback != null) {
                            mWeatherInfoCallback.onWeatherGetComplete(null);
                            WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(3);
                        }
                    } catch (RemoteException e) {
                        Timber.e(logTag + "onWeatherGetComplete error : " + e.getMessage());
                    }
                }
                if (call != null) {
                    call.cancel();
                }
            }

            @Override
            public void onFailure(Call<ResponseWeatherBean> call, @NonNull Throwable t) {
                LogUtils.e(logTag + "requestWeatherByPosition onFailure", t);
                if (wCallback != null) {
                    wCallback.onWeatherRetrieveReady(false);
                }
                String weatherStr = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_WINFO);
                if (!TextUtils.isEmpty(weatherStr)) {
                    WeatherData weatherData = GsonUtil.fromJson(weatherStr, WeatherData.class);
                    if (null != weatherData) {
                        weatherInfo = weatherData;
                        CardData cardData = WeatherManger.getInstance(WeatherApp.getInstance()).conver2CardData(weatherData);
                        try {
                            if (mWeatherInfoCallback != null) {
                                Timber.e(logTag + "requestWeatherByPosition onFailure onWeatherGetComplete");
                                mWeatherInfoCallback.onWeatherGetComplete(weatherData);
                                WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(4, cardData);
                            }
                        } catch (RemoteException e) {
                            Timber.e(logTag + "onWeatherGetComplete error : " + e.getMessage());
                        }
                        return;
                    }
                }
                weatherInfo = new WeatherData();
                errorCode = Constants.NET_SOCKET_ERROR;
                MyNetworkUtil.extracted(t);
                try {
                    if (mWeatherInfoCallback != null) {
                        Timber.e(logTag + "requestWeatherByPosition onFailure onWeatherGetComplete");
                        mWeatherInfoCallback.onWeatherGetComplete(null);
                        WeatherManger.getInstance(WeatherApp.getInstance()).changeCardStatus(3);
                    }
                } catch (RemoteException e) {
                    Timber.e(logTag + "onWeatherGetComplete error : " + e.getMessage());
                }
            }
        });
    }

    public void notifyCityChange(String city) {
        if (TextUtils.isEmpty(city)) {
            requestCity = NAV_ERROR_CODE;
            return;
        }
        if (NAV_ERROR_CODE.equals(city)) { //异常情况
            requestCity = NAV_ERROR_CODE;
            return;
        }
        if (mWeatherInfoCallback != null) {
            requestCity = city;
            mWeatherInfoCallback.onCityGetComplete(city);
            PrefUtils.getInstance().setString(SP_KEY_LAST_LOCATION_CITY, city);
        }
    }

    //设置当前温度替换成墨迹5分钟更新的实时数据，实时数据字段temperature
    private void setCurrentTemp(WeatherData weatherInfo, String fun) {
        try {
            String temperature = weatherInfo.getTemperature();
            weatherInfo.getHourly().get(0).setTemp(temperature);
            Timber.i(logTag + "update_temperature_succeed:" + fun);
        } catch (Exception e) {
            Timber.i(logTag + "update_temperature_fail:" + fun + ">>>>>>>" + e);
            //e.printStackTrace();
        }
    }
}
