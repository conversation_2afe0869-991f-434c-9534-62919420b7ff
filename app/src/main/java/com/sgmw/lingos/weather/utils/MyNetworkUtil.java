package com.sgmw.lingos.weather.utils;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Handler;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.gson.Gson;
import com.sgmw.lingos.weather.R;
import com.sgmw.lingos.weather.WeatherApp;
import com.sgmw.widget.SGMWToast;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

import timber.log.Timber;

/**
 * <pre>
 *     author: Blankj
 *     blog  : http://blankj.com
 *     time  : 2016/08/02
 *     desc  : utils about network
 * </pre>
 */
public final class MyNetworkUtil {
    public static String url = "http://www.baidu.com";
    public static int NET_CNNT_BAIDU_OK = 1; // NetworkAvailable
    public static int NET_CNNT_BAIDU_TIMEOUT = 2; // no NetworkAvailable
    public static int NET_NOT_PREPARE = 3; // Net no ready
    public static int NET_ERROR = 4; //net error
    private static int TIMEOUT = 3000; // TIMEOUT
    private static final String logTag = LOG_TAG + "-NetworkUtil:";
    private static volatile MyNetworkUtil instance;//helper实例

    public static MyNetworkUtil getInstance() {
        if (instance == null) {
            instance = new MyNetworkUtil();
        }
        return instance;
    }

    private ConnectivityManager connectivityManager;

    /**
     * 主进程初始化,防止在使用的时候由于在子进程初始化导致handler的创建异常
     */
    public void init(Context context) {
        connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
    }

    /**
     * Return whether network is connected.
     * <p>Must hold {@code <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />}</p>
     *
     * @return {@code true}: connected<br>{@code false}: disconnected
     */
    public boolean isConnected() {
        NetworkCapabilities networkCapabilities = null;
        if (connectivityManager != null) {
            Network network = connectivityManager.getActiveNetwork();
            if (network != null) {
                networkCapabilities = connectivityManager.getNetworkCapabilities(network);
            }
        }
        return networkCapabilities != null && networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET);
    }

    private static Handler mHandler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            Throwable throwable = (Throwable) msg.obj;
            switch (msg.what) {
                case 1://网络可用
                    throwable.printStackTrace();
                    break;
                case 2://弱网环境，提示网络不给力
                    if (WeatherApp.isWeatherApp())
                        SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail_try_again)).show();
                    break;
                case 3://网络不可用
                    if (WeatherApp.isWeatherApp())
                        SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail)).show();
                    break;
                case 4:// 网络错误
                    if (WeatherApp.isWeatherApp())
                        SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail)).show();
                    break;
            }
        }
    };

    public static void extracted(Throwable throwable) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                int netState = MyNetworkUtil.getNetState(WeatherApp.getInstance());
                Message message = new Message();
                message.what = netState;
                message.obj = throwable;
                mHandler.sendMessage(message);
            }
        }).start();
    }


    public static int getNetState(Context context) {
        try {
            ConnectivityManager connectivity = getInstance().connectivityManager;
            if (connectivity != null) {
                NetworkInfo networkinfo = connectivity.getActiveNetworkInfo();
                if (networkinfo != null) {
                    if (networkinfo.isAvailable() && networkinfo.isConnected()) {
                        if (!isNetworkOnline()) {
                            Log.i(logTag, ">>>>>>>>>getNetState isNetworkOnline>>>>>>false");
                            return NET_CNNT_BAIDU_TIMEOUT;
                        } else {
                            Log.i(logTag, ">>>>>>>>>getNetState isNetworkOnline>>>>>>true");
                            return NET_CNNT_BAIDU_OK;
                        }

                    } else {
                        Log.i(logTag, ">>>>>>>>>getNetState NET_NOT_PREPARE>>>>>>");
                        return NET_NOT_PREPARE;
                    }
                }
            }
        } catch (Exception e) {
            Log.i(logTag, ">>>>>>>>>getNetState Exception>>>>>>" + new Gson().toJson(e));
            //e.printStackTrace();
        }
        Log.i(logTag, ">>>>>>>>>getNetState NET_ERROR>>>>>>");
        return NET_ERROR;
    }

    /**
     * ping "http://www.baidu.com"
     *
     * @return
     */
    static private boolean connectionNetwork() {
        boolean result = false;
        HttpURLConnection httpUrl = null;
        try {
            httpUrl = (HttpURLConnection) new URL(url)
                    .openConnection();
            httpUrl.setConnectTimeout(TIMEOUT);
            httpUrl.connect();
            result = true;
        } catch (IOException e) {
        } finally {
            if (null != httpUrl) {
                httpUrl.disconnect();
            }
            httpUrl = null;
        }
        return result;
    }

    public static boolean isNetworkOnline() {
        Runtime runtime = Runtime.getRuntime();
        try {
            Process ipProcess = runtime.exec("ping -c 3 www.baidu.com");
            int exitValue = ipProcess.waitFor();
            Log.i(logTag, "Avalible,Process:%s" + exitValue + "");
            return (exitValue == 0);
        } catch (IOException | InterruptedException e) {
            Timber.e("%sException:isNetworkOnline", logTag);
            Thread.currentThread().interrupted();
            //e.printStackTrace();
        }
        return false;
    }
}
