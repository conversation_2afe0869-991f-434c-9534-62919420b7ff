package com.sgmw.lingos.weather.helper;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.autoai.avs.crypto.client.CryptoManager;
import com.autoai.avs.crypto.client.interfaces.SocketFactoryChangeListener;
import com.google.gson.JsonObject;
import com.sgmw.entity.ResponseWeatherBean;
import com.sgmw.lingos.weather.BuildConfig;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.server.ApiServer;
import com.sgmw.lingos.weather.server.ApiServerNormal;
import com.sgmw.lingos.weather.utils.Constants;
import com.sgmw.lingos.weather.utils.GlobalENV;
import com.sgmw.lingos.weather.utils.MyNetworkUtil;
import com.sgmw.lingos.weather.utils.OkHttpRetryInterceptor;
import com.sgmw.lingos.weather.utils.SignatureUtils;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import retrofit2.Callback;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import timber.log.Timber;

/*
 * 网络请求封装类，封装网络请求api数据方法
 * 测试环境：http://apigw-test.sgmwcloud.com.cn/vehicle/v1/{ command }
 * 正式环境：https://apigw.sgmwcloud.com.cn/vehicle/v1/{ command }
 * */

public class NetHelper {
    private String TAG = "NetHelper";
    private static final String logTag = LOG_TAG + "-NetHelper:";
    private ApiServer apiServerTest;//测试api接口服务
    private ApiServerNormal apiServerNormal;//正式api接口服务
    private boolean debugApi;//是否为测试api的数据
    private Context context;//上下文
    private OkHttpClient okHttpClient;//网络请求客户端
    private Retrofit retrofit;//retrofit创建网络请求
    private static final int DEFAULT_TIMEOUT = 50;//超时时间、s

    private static volatile NetHelper instance;//helper实例

    public static NetHelper getInstance() {
        if (instance == null) {
            instance = new NetHelper();
        }
        return instance;
    }

    public NetHelper init(Context context) {
        Log.i(TAG, "58:init: start time " + System.currentTimeMillis());
        this.context = context;
        debugApi = GlobalENV.initApiMode(context);
        OkHttpClient.Builder builder=new OkHttpClient.Builder();
//        initClient(builder);
        CryptoManager.INSTANCE.setSocketFactoryChangeListener(new SocketFactoryChangeListener() {
            @Override
            public void onSocketFactoryChange() {
                Timber.d(logTag + "the crypto is connected");
                initClient(builder);
                WeatherManger.getInstance(context).getNavAreaInfo();
            }
        });
        return this;
    }
    private void initClient(OkHttpClient.Builder builder){
        if (CryptoManager.INSTANCE.getSSLSocketFactory()!=null&&CryptoManager.INSTANCE.getTrustManager()!=null){
            builder.sslSocketFactory(CryptoManager.INSTANCE.getSSLSocketFactory(),CryptoManager.INSTANCE.getTrustManager());
        }
        okHttpClient =builder.connectTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(new OkHttpRetryInterceptor.Builder()
                        .buildRetryCount(3) // 根据isRetryEnabled决定是否启用重试
                        .build())
                .addInterceptor(new Interceptor() {
                    @NonNull
                    @Override
                    public Response intercept(@NonNull Chain chain) throws IOException {
                        Request request = chain.request().newBuilder()
                                .header("apiKey",GlobalENV.initApiMode(context)?Constants.API_KEY_TEST:Constants.API_KEY_NORMAL )
                                .build();
                        return chain.proceed(request);
                    }
                })
                .build();
        retrofit = new Retrofit.Builder().client(okHttpClient).baseUrl(debugApi ? Constants.BASE_URL_TEST : Constants.BASE_URL_NORMAL)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        Timber.d(logTag + "debugApi=%s", debugApi);
        if (debugApi) {
            apiServerTest = retrofit.create(ApiServer.class);
        } else {
            apiServerNormal = retrofit.create(ApiServerNormal.class);
        }
        Log.i(TAG, "71:init: end time " + System.currentTimeMillis());
    }

    /*
     * 根据CityId获取天气
     *
     * */
    private RequestBody createBodyForWeather(String cid) {
        final String vin = TextUtils.isEmpty(GlobalENV.getInstance().getCarVin()) ? "vehicleSN" : GlobalENV.getInstance().getCarVin();
        String sign = SignatureUtils.MD5(SignatureUtils.getSign(debugApi, vin, cid));
        JsonObject json = new JsonObject();
        json.addProperty("vehicleSN", vin);
        json.addProperty("domainKey", debugApi ? Constants.API_KEY_TEST : Constants.API_KEY_NORMAL);
        json.addProperty("sign", sign);
        json.addProperty("districtId", cid);
        Timber.d(logTag + "createBodyForWeather%s", json.toString());
        return RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json.toString());

    }

    /*
     *
     * 根据定位经纬度获取天气
     *
     * */
    private RequestBody createBodyForWeather(String lat, String lon, String time) {
        String versions = time + BuildConfig.VERSION_NAME;
        final String vin = TextUtils.isEmpty(GlobalENV.getInstance().getCarVin()) ? "vehicleSN" : GlobalENV.getInstance().getCarVin();
        String sign = SignatureUtils.MD5(SignatureUtils.getSign(debugApi, vin, lat, lon, versions));
        JsonObject json = new JsonObject();
        json.addProperty("vehicleSN", vin);
        json.addProperty("domainKey", debugApi ? Constants.API_KEY_TEST : Constants.API_KEY_NORMAL);
        json.addProperty("sign", sign);
        json.addProperty("lon", lon);
        json.addProperty("lat", lat);
        json.addProperty("versions", versions);
        Timber.d(logTag + "createBodyForWeather");
        return RequestBody.create(MediaType.parse("application/json; charset=utf-8"), json.toString());
    }

    public void getWeatherByPosition(String lat, String lon, String time, Callback<ResponseWeatherBean> caller) {
        if (context != null) {
            if (!MyNetworkUtil.getInstance().isConnected()) {
                caller.onFailure(null, new Throwable("Network is not Connected"));
//                context.getMainExecutor().execute(() -> {
//                    if (WeatherApp.isWeatherApp()) {
//                        SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail)).show();
//                    }
//                });

                return;
            }
        }
        Log.i(logTag, ">>>>>>>>>>>>>debugApi>>>>>>>" + debugApi);
        if (debugApi) {
            if (apiServerTest != null) {
                apiServerTest.getWeatherByPosition(createBodyForWeather(lat, lon, time)).enqueue(caller);
            } else {
                caller.onFailure(null, new Throwable("the apiservice is null"));
                Log.e("TAG", "154:getWeatherByPosition:  the apiservice is null");
            }
        } else {
            if (apiServerNormal != null) {
                apiServerNormal.getWeatherByPosition(createBodyForWeather(lat, lon, time)).enqueue(caller);
            } else {
                caller.onFailure(null, new Throwable("apiServerNormal is null"));
                Log.e("TAG", "154:getWeatherByPosition:  apiServerNormal is null");
            }
        }
    }

}
