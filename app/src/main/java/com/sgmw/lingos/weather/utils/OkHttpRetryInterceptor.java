package com.sgmw.lingos.weather.utils;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.util.Log;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class OkHttpRetryInterceptor implements Interceptor{

    private int mMaxRetryCount;
    private long mRetryInterval;
    private static final String logTag = LOG_TAG + "-OkHttpRetryInterceptor:";
    private volatile int retryCount;
    public OkHttpRetryInterceptor(int maxRetryCount, long retryInterval) {
        mMaxRetryCount = maxRetryCount;
        mRetryInterval = retryInterval;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        retryCount = 0;
        Request request = chain.request();
        Response response = chain.proceed(request);
        while (!response.isSuccessful() && retryCount < mMaxRetryCount) {
            Log.i(logTag, ">>>>>>>>>>>重试 retryNum>>>>>>>>>>" + retryCount);
            retryCount++;
            response.close();
            response = chain.proceed(request);
        }
        return response;
    }

    private Response doRequest(Chain chain, Request request) {
        try {
            return chain.proceed(request);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static class Builder {

        private int mRetryCount = 3;
        private long mRetryInterval = 1000;

        public Builder buildRetryCount(int retryCount){
            this.mRetryCount = retryCount;
            return this;
        }

        public Builder buildRetryInterval(long retryInterval){
            this.mRetryInterval = retryInterval;
            return this;
        }

        public OkHttpRetryInterceptor build(){
            return new OkHttpRetryInterceptor(mRetryCount,mRetryInterval);
        }

    }

}

