package com.sgmw.lingos.weather.utils;

import com.sgmw.lingos.weather.R;

import java.util.HashMap;

public class Constants {
    public static final String ID = "Weather";
    public static final String NAME = "WeatherService";

    // 沙盒
    public static final String BASE_URL_TEST = "http://apigw-test.sgmwcloud.com.cn/vehicle/V1/";
    public static final String API_KEY_TEST = "AD3FE1315192093DD30E1978DED97FD7A15798D2918AE152A3E7E01D8073521A";
    // 正式
    public static final String BASE_URL_NORMAL = "https://apigw-muauth-apn2.sgmwcloud.com.cn:8050/vehicle/V1/";
    public static final String API_KEY_NORMAL = "BC9BDEC46CE1D83D3D1BBD60451099DC702C9354FC4980046D1C4AA02DDEE722";

    public static final String TOKEN_ROOT = "ROOT";
    public static final String TOKEN_USER = "USER";

    //返回码
    public final static int RESULT_CODE = 100;
    //请求码
    public final static int REQUEST_CODE = 101;

    public static final int NET_REQUEST_SUCCESS = 0;
    public static final int NET_REQUEST_PARAM_INVALID = 1001;
    public static final int NET_REQUEST_PARAM_ERROR = 2000;
    public static final int NET_REQUEST_TIMEOUT = 10050;
    public static final int NET_REPONSE_NULL = 10051;
    public static final int NET_SOCKET_ERROR = -1;


    public final static String CITY_FRAGMENT = "CityFragment";
    public final static String MAIN_FRAGMENT = "MainFragment";
    public final static String SEARCH_FRAGMENT = "SearchFragment";

    //最近的定位坐标
    public final static String SP_KEY_LAST_LOCATION_LAT = "SP_KEY_LAST_LOCATION_LAT";
    public final static String SP_KEY_LAST_LOCATION_LON = "SP_KEY_LAST_LOCATION_LON";
    //最近的定位地址
    public final static String SP_KEY_LAST_LOCATION_CITY = "SP_KEY_LAST_LOCATION_CITY";
    //最近的定位坐标天气信息
    public final static String SP_KEY_LAST_LOCATION_WINFO = "SP_KEY_LAST_LOCATION_WINFO";

    //最近的定位坐标天气日升信息
    public final static String SP_KEY_LAST_LOCATION_SUNRISE_INFO = "SP_KEY_LAST_LOCATION_SUNRISE_INFO";

    //最近的定位坐标天气日落信息
    public final static String SP_KEY_LAST_LOCATION_SUNSET_INFO = "SP_KEY_LAST_LOCATION_SUNSET_INFO";


    //最近的更新时间
    public final static String SP_KEY_LAST_UPDATE_TIME = "SP_KEY_LAST_UPDATE_TIME";
    //上一次的生活指数
    public final static String SP_KEY_LAST_LIVE_INDEX = "SP_KEY_LAST_LIVE_INDEX";
    //车架号
    public final static String SP_KEY_CAR_VIN = "SP_KEY_CAR_VIN";
    //收藏的城市名
    public final static String SP_KEY_COLLECT_CITY = "SP_KEY_COLLECT_CITY";
    //收藏的城市对应的ID
    public final static String SP_KEY_COLLECT_CITY_ID = "SP_KEY_COLLECT_CITY_ID";

    //第一次进入，没有授权或者没有定位坐标时，默认显示这个地址的天气
    public final static String DEFAULT_CITY_NAME = "北京市东城区";
    //高德坐标拾取：天安门的坐标
    public final static String DEFAULT_CITY_LAT = "39.909197";
    public final static String DEFAULT_CITY_LON = "116.397557";

    public final static String LOG_TAG = "SGMWWeather";

    public final static String LOCATION_TIME = "location_time";

    /**
     * 车系
     */
    public static final String CAR_SERIES = "E260S";

    /**
     * 滚动广播
     * */
    public static final String SCROLL_ACTION = "com.sgmw.view.scrollview.action";
    public static final String SCROLL_STATUS = "scrollstatus";
    public static final int SCROLL_TOP = 1;
    public static final int SCROLL_BOTTOM = 2;
    public static final String SETTINGS_KEY_LOGIN = "user_center/login";

    //Condition ID和对标的对应关系
//    public static final HashMap<String, Integer> WEATHER_DES_ICON = new HashMap<>();
//
//    static {
//        WEATHER_DES_ICON.put("晴", R.mipmap.ic_weather_0);// 晴
//        WEATHER_DES_ICON.put("大部晴朗", R.mipmap.ic_weather_0);// 大部晴朗
//        WEATHER_DES_ICON.put("多云", R.mipmap.ic_weather_1);// 多云
//        WEATHER_DES_ICON.put("少云", R.mipmap.ic_weather_1);//少云
//        WEATHER_DES_ICON.put("阴", R.mipmap.ic_overcast);//阴
//        WEATHER_DES_ICON.put("阵雨", R.mipmap.ic_showers);//阵雨
//        WEATHER_DES_ICON.put("局部阵雨", R.mipmap.ic_scattered_showers);//局部阵雨
//        WEATHER_DES_ICON.put("小阵雨", R.mipmap.ic_light_showers);//小阵雨
//        WEATHER_DES_ICON.put("强阵雨", R.mipmap.ic_heavy_showers);//强阵雨
//        WEATHER_DES_ICON.put("阵雪", R.mipmap.ic_snow_showers);//阵雪
//        WEATHER_DES_ICON.put("小阵雪", R.mipmap.ic_light_snow_showers);//小阵雪
//        WEATHER_DES_ICON.put("雾", R.mipmap.ic_fog);//雾
//        WEATHER_DES_ICON.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
//        WEATHER_DES_ICON.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
//        WEATHER_DES_ICON.put("浮尘", R.mipmap.ic_dust);//浮尘
//        WEATHER_DES_ICON.put("尘卷风", R.mipmap.ic_tornado);//尘卷风
//        WEATHER_DES_ICON.put("扬沙", R.mipmap.ic_sand);//扬沙
//        WEATHER_DES_ICON.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
//        WEATHER_DES_ICON.put("霾", R.mipmap.ic_haze);//霾
//        WEATHER_DES_ICON.put("雷阵雨", R.mipmap.ic_thundershower);//雷阵雨
//        WEATHER_DES_ICON.put("雷电", R.mipmap.ic_lightning);//雷电
//        WEATHER_DES_ICON.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
//        WEATHER_DES_ICON.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail);//雷阵雨伴有冰雹
//        WEATHER_DES_ICON.put("冰雹", R.mipmap.ic_hail);//冰雹
//        WEATHER_DES_ICON.put("冰针", R.mipmap.ic_needle_ice);//冰针
//        WEATHER_DES_ICON.put("冰粒", R.mipmap.ic_icy);//冰粒
//        WEATHER_DES_ICON.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
//        WEATHER_DES_ICON.put("小雨", R.mipmap.ic_light_rain);//小雨
//        WEATHER_DES_ICON.put("中雨", R.mipmap.ic_rain);//中雨
//        WEATHER_DES_ICON.put("大雨", R.mipmap.ic_heavy_rain);//大雨
//        WEATHER_DES_ICON.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
//        WEATHER_DES_ICON.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
//        WEATHER_DES_ICON.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
//        WEATHER_DES_ICON.put("小雪", R.mipmap.ic_light_snow);//小雪
//        WEATHER_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
//        WEATHER_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
//        WEATHER_DES_ICON.put("大雪", R.mipmap.ic_heavy_snow);//大雪
//        WEATHER_DES_ICON.put("暴雪", R.mipmap.ic_blizzard);//暴雪
//        WEATHER_DES_ICON.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
//        WEATHER_DES_ICON.put("雪", R.mipmap.ic_snow);//雪
//        WEATHER_DES_ICON.put("雨", R.mipmap.ic_rain);//雨
//        WEATHER_DES_ICON.put("小到中雨", R.mipmap.ic_rain);//小到中雨
//        WEATHER_DES_ICON.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
//        WEATHER_DES_ICON.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
//        WEATHER_DES_ICON.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
//    }

    //日落Condition ID和对标的对应关系
    public static final HashMap<String, Integer> WEATHER_DES_ICON_NIGHT = new HashMap<>();

    static {
        WEATHER_DES_ICON_NIGHT.put("晴", R.mipmap.ic_sunny_dark);// 晴
        WEATHER_DES_ICON_NIGHT.put("大部晴朗", R.mipmap.ic_mostly_sunny_dark);// 大部晴朗
        WEATHER_DES_ICON_NIGHT.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_DES_ICON_NIGHT.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_DES_ICON_NIGHT.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_DES_ICON_NIGHT.put("阵雨", R.mipmap.ic_showers_dark);//阵雨
        WEATHER_DES_ICON_NIGHT.put("局部阵雨", R.mipmap.ic_scattered_showers_dark);//局部阵雨
        WEATHER_DES_ICON_NIGHT.put("小阵雨", R.mipmap.ic_light_showers_dark);//小阵雨
        WEATHER_DES_ICON_NIGHT.put("强阵雨", R.mipmap.ic_heavy_showers_dark);//强阵雨
        WEATHER_DES_ICON_NIGHT.put("阵雪", R.mipmap.ic_snow_showers_dark);//阵雪
        WEATHER_DES_ICON_NIGHT.put("小阵雪", R.mipmap.ic_light_snow_showers_dark);//小阵雪
        WEATHER_DES_ICON_NIGHT.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_DES_ICON_NIGHT.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_DES_ICON_NIGHT.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_DES_ICON_NIGHT.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_DES_ICON_NIGHT.put("尘卷风", R.mipmap.ic_tornado);//尘卷风
        WEATHER_DES_ICON_NIGHT.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_DES_ICON_NIGHT.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_DES_ICON_NIGHT.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_DES_ICON_NIGHT.put("雷阵雨", R.mipmap.ic_thundershower_dark);//雷阵雨
        WEATHER_DES_ICON_NIGHT.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_DES_ICON_NIGHT.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_DES_ICON_NIGHT.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_dark);//雷阵雨伴有冰雹
        WEATHER_DES_ICON_NIGHT.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_DES_ICON_NIGHT.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_DES_ICON_NIGHT.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_DES_ICON_NIGHT.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_DES_ICON_NIGHT.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_DES_ICON_NIGHT.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_DES_ICON_NIGHT.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_DES_ICON_NIGHT.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_DES_ICON_NIGHT.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_DES_ICON_NIGHT.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_DES_ICON_NIGHT.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_DES_ICON_NIGHT.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_DES_ICON_NIGHT.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_DES_ICON_NIGHT.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_DES_ICON_NIGHT.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_DES_ICON_NIGHT.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_DES_ICON_NIGHT.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_DES_ICON_NIGHT.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_DES_ICON_NIGHT.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_DES_ICON_NIGHT.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    /**
     * 天气App大背景图 start
     */
    //Condition ID和天气背景图片的对应关系
    public static final HashMap<String, Integer> WEATHER_DES_IMAGE = new HashMap<>();

    static {
        WEATHER_DES_IMAGE.put("晴", R.mipmap.img_sunny);// 晴

        WEATHER_DES_IMAGE.put("大部晴朗", R.mipmap.img_mostly_sunny);// 大部晴朗
        WEATHER_DES_IMAGE.put("多云", R.mipmap.img_cloudy);// 多云

        WEATHER_DES_IMAGE.put("少云", R.mipmap.img_partly_cloudy);//少云
        WEATHER_DES_IMAGE.put("阴", R.mipmap.img_overcast);//阴
        WEATHER_DES_IMAGE.put("阵雨", R.mipmap.img_showers);//阵雨
        WEATHER_DES_IMAGE.put("局部阵雨", R.mipmap.img_scattered_showers);//局部阵雨
        WEATHER_DES_IMAGE.put("小阵雨", R.mipmap.img_light_showers);//小阵雨
        WEATHER_DES_IMAGE.put("强阵雨", R.mipmap.img_heavy_showers);//强阵雨
        WEATHER_DES_IMAGE.put("阵雪", R.mipmap.img_snow_showers);//阵雪
        WEATHER_DES_IMAGE.put("小阵雪", R.mipmap.img_light_snow_showers);//小阵雪
        WEATHER_DES_IMAGE.put("雾", R.mipmap.img_fog);//雾
        WEATHER_DES_IMAGE.put("冻雾", R.mipmap.img_freezing_fog);//冻雾
        WEATHER_DES_IMAGE.put("沙尘暴", R.mipmap.img_sandstorm);//沙尘暴
        WEATHER_DES_IMAGE.put("浮尘", R.mipmap.img_dust);//浮尘
        WEATHER_DES_IMAGE.put("尘卷风", R.mipmap.img_duststorm);//尘卷风
        WEATHER_DES_IMAGE.put("扬沙", R.mipmap.img_sand);//扬沙
        WEATHER_DES_IMAGE.put("强沙尘暴", R.mipmap.img_heavy_sandstorm);//强沙尘暴
        WEATHER_DES_IMAGE.put("霾", R.mipmap.img_haze);//霾
        WEATHER_DES_IMAGE.put("雷阵雨", R.mipmap.img_thundershower);//雷阵雨

        WEATHER_DES_IMAGE.put("雷电", R.mipmap.img_lighting);//雷电
        WEATHER_DES_IMAGE.put("雷暴", R.mipmap.img_thunderstorm);//雷暴
        WEATHER_DES_IMAGE.put("雷阵雨伴有冰雹", R.mipmap.img_thundershower_with_hail);//雷阵雨伴有冰雹
        WEATHER_DES_IMAGE.put("冰雹", R.mipmap.img_hail);//冰雹
        WEATHER_DES_IMAGE.put("冰针", R.mipmap.img_needle_ice);//冰针
        WEATHER_DES_IMAGE.put("冰粒", R.mipmap.img_icy);//冰粒
        WEATHER_DES_IMAGE.put("雨夹雪", R.mipmap.img_sleet);//雨夹雪
        WEATHER_DES_IMAGE.put("小雨", R.mipmap.img_light_rain);//小雨
        WEATHER_DES_IMAGE.put("中雨", R.mipmap.img_rain);//中雨
        WEATHER_DES_IMAGE.put("大雨", R.mipmap.img_heavy_rain);//大雨
        WEATHER_DES_IMAGE.put("暴雨", R.mipmap.img_rainstorm);//暴雨
        WEATHER_DES_IMAGE.put("大暴雨", R.mipmap.img_heavy_rainstorm);//大暴雨
        WEATHER_DES_IMAGE.put("特大暴雨", R.mipmap.img_extreme_rainstorm);//特大暴雨
        WEATHER_DES_IMAGE.put("小雪", R.mipmap.img_light_snow);//小雪
        WEATHER_DES_IMAGE.put("中雪", R.mipmap.img_snow);//中雪
        WEATHER_DES_IMAGE.put("大雪", R.mipmap.img_heavy_snow);//大雪
        WEATHER_DES_IMAGE.put("暴雪", R.mipmap.img_blizzard);//暴雪
        WEATHER_DES_IMAGE.put("冻雨", R.mipmap.img_freezing_rain);//冻雨
        WEATHER_DES_IMAGE.put("小雨", R.mipmap.img_light_rain);//小雨
        WEATHER_DES_IMAGE.put("中雨", R.mipmap.img_rain);//中雨
        WEATHER_DES_IMAGE.put("大雨", R.mipmap.img_heavy_rain);//大雨
        WEATHER_DES_IMAGE.put("大暴雨", R.mipmap.img_heavy_rainstorm);//大暴雨
        WEATHER_DES_IMAGE.put("大暴雨", R.mipmap.img_heavy_rainstorm);//大暴雨
        WEATHER_DES_IMAGE.put("小雪", R.mipmap.img_light_snow);//小雪
        WEATHER_DES_IMAGE.put("大雪", R.mipmap.img_heavy_snow);//大雪
        WEATHER_DES_IMAGE.put("雪", R.mipmap.img_snow);//雪
        WEATHER_DES_IMAGE.put("雨", R.mipmap.img_rain);//雨
        WEATHER_DES_IMAGE.put("雾", R.mipmap.img_fog);//雾
        WEATHER_DES_IMAGE.put("雷阵雨", R.mipmap.img_thundershower);//雷阵雨

        WEATHER_DES_IMAGE.put("小到中雨", R.mipmap.img_rain);//小到中雨
        WEATHER_DES_IMAGE.put("中到大雨", R.mipmap.img_heavy_rain);//中到大雨
        WEATHER_DES_IMAGE.put("大到暴雨", R.mipmap.img_rainstorm);//大到暴雨
        WEATHER_DES_IMAGE.put("小到中雪", R.mipmap.img_snow);//小到中雪
    }

    //日落Condition ID和天气背景图片的对应关系
    public static final HashMap<String, Integer> WEATHER_DES_IMAGE_NIGHT = new HashMap<>();

    static {
        WEATHER_DES_IMAGE_NIGHT.put("晴", R.mipmap.img_sunny_dark);// 晴

        WEATHER_DES_IMAGE_NIGHT.put("大部晴朗", R.mipmap.img_mostly_sunny_dark);// 大部晴朗
        WEATHER_DES_IMAGE_NIGHT.put("多云", R.mipmap.img_cloudy_dark);// 多云

        WEATHER_DES_IMAGE_NIGHT.put("少云", R.mipmap.img_partly_cloudy_dark);//少云
        WEATHER_DES_IMAGE_NIGHT.put("阴", R.mipmap.img_overcast_dark);//阴
        WEATHER_DES_IMAGE_NIGHT.put("阵雨", R.mipmap.img_showers_dark);//阵雨
        WEATHER_DES_IMAGE_NIGHT.put("局部阵雨", R.mipmap.img_scattered_showers_dark);//局部阵雨
        WEATHER_DES_IMAGE_NIGHT.put("小阵雨", R.mipmap.img_light_showers_dark);//小阵雨
        WEATHER_DES_IMAGE_NIGHT.put("强阵雨", R.mipmap.img_heavy_showers_dark);//强阵雨
        WEATHER_DES_IMAGE_NIGHT.put("阵雪", R.mipmap.img_snow_showers_dark);//阵雪
        WEATHER_DES_IMAGE_NIGHT.put("小阵雪", R.mipmap.img_light_snow_showers_dark);//小阵雪
        WEATHER_DES_IMAGE_NIGHT.put("雾", R.mipmap.img_fog_dark);//雾
        WEATHER_DES_IMAGE_NIGHT.put("冻雾", R.mipmap.img_freezing_fog_dark);//冻雾
        WEATHER_DES_IMAGE_NIGHT.put("沙尘暴", R.mipmap.img_sandstorm_dark);//沙尘暴
        WEATHER_DES_IMAGE_NIGHT.put("浮尘", R.mipmap.img_dust_dark);//浮尘
        WEATHER_DES_IMAGE_NIGHT.put("尘卷风", R.mipmap.img_duststorm_dark);//尘卷风
        WEATHER_DES_IMAGE_NIGHT.put("扬沙", R.mipmap.img_sand_dark);//扬沙
        WEATHER_DES_IMAGE_NIGHT.put("强沙尘暴", R.mipmap.img_heavy_sandstorm_dark);//强沙尘暴
        WEATHER_DES_IMAGE_NIGHT.put("霾", R.mipmap.img_haze_dark);//霾
        WEATHER_DES_IMAGE_NIGHT.put("阴", R.mipmap.img_overcast_dark);//阴
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨", R.mipmap.img_thundershower_dark);//雷阵雨
        WEATHER_DES_IMAGE_NIGHT.put("雷电", R.mipmap.img_lighting_dark);//雷电
        WEATHER_DES_IMAGE_NIGHT.put("雷暴", R.mipmap.img_thunderstorm_dark);//雷暴
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨伴有冰雹", R.mipmap.img_thundershower_with_hail_dark);//雷阵雨伴有冰雹
        WEATHER_DES_IMAGE_NIGHT.put("冰雹", R.mipmap.img_hail_dark);//冰雹
        WEATHER_DES_IMAGE_NIGHT.put("冰针", R.mipmap.img_needle_ice_dark);//冰针
        WEATHER_DES_IMAGE_NIGHT.put("冰粒", R.mipmap.img_icy_dark);//冰粒
        WEATHER_DES_IMAGE_NIGHT.put("雨夹雪", R.mipmap.img_sleet_dark);//雨夹雪
        WEATHER_DES_IMAGE_NIGHT.put("小雨", R.mipmap.img_light_rain_dark);//小雨
        WEATHER_DES_IMAGE_NIGHT.put("中雨", R.mipmap.img_rain_dark);//中雨
        WEATHER_DES_IMAGE_NIGHT.put("大雨", R.mipmap.img_heavy_rain_dark);//大雨
        WEATHER_DES_IMAGE_NIGHT.put("暴雨", R.mipmap.img_rainstorm_dark);//暴雨
        WEATHER_DES_IMAGE_NIGHT.put("大暴雨", R.mipmap.img_heavy_rainstorm_dark);//大暴雨
        WEATHER_DES_IMAGE_NIGHT.put("特大暴雨", R.mipmap.img_extreme_rainstorm_dark);//特大暴雨
        WEATHER_DES_IMAGE_NIGHT.put("小雪", R.mipmap.img_light_snow_dark);//小雪
        WEATHER_DES_IMAGE_NIGHT.put("小雪", R.mipmap.img_light_snow_dark);//小雪
        WEATHER_DES_IMAGE_NIGHT.put("中雪", R.mipmap.img_snow_dark);//中雪
        WEATHER_DES_IMAGE_NIGHT.put("中雪", R.mipmap.img_snow_dark);//中雪
        WEATHER_DES_IMAGE_NIGHT.put("大雪", R.mipmap.img_heavy_snow_dark);//大雪
        WEATHER_DES_IMAGE_NIGHT.put("暴雪", R.mipmap.img_blizzard_dark);//暴雪
        WEATHER_DES_IMAGE_NIGHT.put("冻雨", R.mipmap.img_freezing_rain_dark);//冻雨
        WEATHER_DES_IMAGE_NIGHT.put("冻雨", R.mipmap.img_freezing_rain_dark);//冻雨
        WEATHER_DES_IMAGE_NIGHT.put("小雨", R.mipmap.img_light_rain_dark);//小雨
        WEATHER_DES_IMAGE_NIGHT.put("中雨", R.mipmap.img_rain_dark);//中雨
        WEATHER_DES_IMAGE_NIGHT.put("大雨", R.mipmap.img_heavy_rain_dark);//大雨
        WEATHER_DES_IMAGE_NIGHT.put("大暴雨", R.mipmap.img_heavy_rainstorm_dark);//大暴雨
        WEATHER_DES_IMAGE_NIGHT.put("大暴雨", R.mipmap.img_heavy_rainstorm_dark);//大暴雨
        WEATHER_DES_IMAGE_NIGHT.put("小雪", R.mipmap.img_light_snow_dark);//小雪
        WEATHER_DES_IMAGE_NIGHT.put("小雪", R.mipmap.img_light_snow_dark);//小雪
        WEATHER_DES_IMAGE_NIGHT.put("小雪", R.mipmap.img_light_snow_dark);//小雪
        WEATHER_DES_IMAGE_NIGHT.put("大雪", R.mipmap.img_heavy_snow_dark);//大雪
        WEATHER_DES_IMAGE_NIGHT.put("大雪", R.mipmap.img_heavy_snow_dark);//大雪
        WEATHER_DES_IMAGE_NIGHT.put("大雪", R.mipmap.img_heavy_snow_dark);//大雪
        WEATHER_DES_IMAGE_NIGHT.put("雪", R.mipmap.img_snow_dark);//雪
        WEATHER_DES_IMAGE_NIGHT.put("雨", R.mipmap.img_rain_dark);//雨
        WEATHER_DES_IMAGE_NIGHT.put("霾", R.mipmap.img_haze_dark);//霾
        WEATHER_DES_IMAGE_NIGHT.put("多云", R.mipmap.img_cloudy_dark);//多云
        WEATHER_DES_IMAGE_NIGHT.put("多云", R.mipmap.img_cloudy_dark);//多云
        WEATHER_DES_IMAGE_NIGHT.put("多云", R.mipmap.img_cloudy_dark);//多云
        WEATHER_DES_IMAGE_NIGHT.put("雾", R.mipmap.img_fog_dark);//雾
        WEATHER_DES_IMAGE_NIGHT.put("雾", R.mipmap.img_fog_dark);//雾
        WEATHER_DES_IMAGE_NIGHT.put("阴", R.mipmap.img_overcast_dark);//阴
        WEATHER_DES_IMAGE_NIGHT.put("阵雨", R.mipmap.img_showers_dark);//阵雨
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨", R.mipmap.img_thundershower_dark);//雷阵雨
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨", R.mipmap.img_thundershower_dark);//雷阵雨
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨", R.mipmap.img_thundershower_dark);//雷阵雨
        WEATHER_DES_IMAGE_NIGHT.put("雷阵雨", R.mipmap.img_thundershower_dark);//雷阵雨
        WEATHER_DES_IMAGE_NIGHT.put("小到中雨", R.mipmap.img_rain_dark);//小到中雨
        WEATHER_DES_IMAGE_NIGHT.put("中到大雨", R.mipmap.img_heavy_rain_dark);//中到大雨
        WEATHER_DES_IMAGE_NIGHT.put("大到暴雨", R.mipmap.img_rainstorm_dark);//大到暴雨
        WEATHER_DES_IMAGE_NIGHT.put("小到中雪", R.mipmap.img_snow_dark);//小到中雪
    }
    /**
     * 天气App大背景图 end
     */


    //Condition name和天气icon的对应关系
    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON.put("晴", R.mipmap.ic_sunny);// 晴
        WEATHER_NAME_DES_ICON.put("大部晴朗", R.mipmap.ic_mostly_sunny);// 大部晴朗
        WEATHER_NAME_DES_ICON.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON.put("阵雨", R.mipmap.ic_showers);//阵雨
        WEATHER_NAME_DES_ICON.put("局部阵雨", R.mipmap.ic_scattered_showers);//局部阵雨
        WEATHER_NAME_DES_ICON.put("小阵雨", R.mipmap.ic_light_showers);//小阵雨
        WEATHER_NAME_DES_ICON.put("强阵雨", R.mipmap.ic_heavy_showers);//强阵雨
        WEATHER_NAME_DES_ICON.put("阵雪", R.mipmap.ic_snow_showers);//阵雪
        WEATHER_NAME_DES_ICON.put("小阵雪", R.mipmap.ic_light_snow_showers);//小阵雪
        WEATHER_NAME_DES_ICON.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON.put("雷阵雨", R.mipmap.ic_thundershower);//雷阵雨
        WEATHER_NAME_DES_ICON.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    //日落Condition name和天气icon的对应关系
    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON_NIGHT = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON_NIGHT.put("晴", R.mipmap.ic_sunny_dark);// 晴
        WEATHER_NAME_DES_ICON_NIGHT.put("大部晴朗", R.mipmap.ic_mostly_sunny_dark);// 大部晴朗
        WEATHER_NAME_DES_ICON_NIGHT.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON_NIGHT.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON_NIGHT.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON_NIGHT.put("阵雨", R.mipmap.ic_showers_dark);//阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("局部阵雨", R.mipmap.ic_scattered_showers_dark);//局部阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小阵雨", R.mipmap.ic_light_showers_dark);//小阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("强阵雨", R.mipmap.ic_heavy_showers_dark);//强阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("阵雪", R.mipmap.ic_snow_showers_dark);//阵雪
        WEATHER_NAME_DES_ICON_NIGHT.put("小阵雪", R.mipmap.ic_light_snow_showers_dark);//小阵雪
        WEATHER_NAME_DES_ICON_NIGHT.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON_NIGHT.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON_NIGHT.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON_NIGHT.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON_NIGHT.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON_NIGHT.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON_NIGHT.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON_NIGHT.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON_NIGHT.put("雷阵雨", R.mipmap.ic_thundershower_dark);//雷阵雨
        WEATHER_NAME_DES_ICON_NIGHT.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON_NIGHT.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON_NIGHT.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_dark);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON_NIGHT.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON_NIGHT.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON_NIGHT.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON_NIGHT.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON_NIGHT.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON_NIGHT.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON_NIGHT.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON_NIGHT.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON_NIGHT.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON_NIGHT.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON_NIGHT.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON_NIGHT.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON_NIGHT.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON_NIGHT.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON_NIGHT.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON_NIGHT.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    public static final HashMap<String, Integer> WEATHER_NAME_DES_ICON_DAY = new HashMap<>();

    static {
        WEATHER_NAME_DES_ICON_DAY.put("晴", R.mipmap.ic_sunny_day);// 晴
        WEATHER_NAME_DES_ICON_DAY.put("大部晴朗", R.mipmap.ic_mostly_sunny_day);// 大部晴朗
        WEATHER_NAME_DES_ICON_DAY.put("多云", R.mipmap.ic_cloudy);// 多云
        WEATHER_NAME_DES_ICON_DAY.put("少云", R.mipmap.ic_partly_cloudy);//少云
        WEATHER_NAME_DES_ICON_DAY.put("阴", R.mipmap.ic_overcast);//阴
        WEATHER_NAME_DES_ICON_DAY.put("阵雨", R.mipmap.ic_showers_day);//阵雨
        WEATHER_NAME_DES_ICON_DAY.put("局部阵雨", R.mipmap.ic_scattered_showers_day);//局部阵雨
        WEATHER_NAME_DES_ICON_DAY.put("小阵雨", R.mipmap.ic_light_showers_day);//小阵雨
        WEATHER_NAME_DES_ICON_DAY.put("强阵雨", R.mipmap.ic_heavy_showers_day);//强阵雨
        WEATHER_NAME_DES_ICON_DAY.put("阵雪", R.mipmap.ic_snow_showers_day);//阵雪
        WEATHER_NAME_DES_ICON_DAY.put("小阵雪", R.mipmap.ic_light_snow_showers_day);//小阵雪
        WEATHER_NAME_DES_ICON_DAY.put("雾", R.mipmap.ic_fog);//雾
        WEATHER_NAME_DES_ICON_DAY.put("冻雾", R.mipmap.ic_freezing_fog);//冻雾
        WEATHER_NAME_DES_ICON_DAY.put("沙尘暴", R.mipmap.ic_sandstorm);//沙尘暴
        WEATHER_NAME_DES_ICON_DAY.put("浮尘", R.mipmap.ic_dust);//浮尘
        WEATHER_NAME_DES_ICON_DAY.put("尘卷风", R.mipmap.ic_duststorm);//尘卷风
        WEATHER_NAME_DES_ICON_DAY.put("扬沙", R.mipmap.ic_sand);//扬沙
        WEATHER_NAME_DES_ICON_DAY.put("强沙尘暴", R.mipmap.ic_heavy_sandstorm);//强沙尘暴
        WEATHER_NAME_DES_ICON_DAY.put("霾", R.mipmap.ic_haze);//霾
        WEATHER_NAME_DES_ICON_DAY.put("雷阵雨", R.mipmap.ic_thundershower_day);//雷阵雨
        WEATHER_NAME_DES_ICON_DAY.put("雷电", R.mipmap.ic_lightning);//雷电
        WEATHER_NAME_DES_ICON_DAY.put("雷暴", R.mipmap.ic_thunderstorm);//雷暴
        WEATHER_NAME_DES_ICON_DAY.put("雷阵雨伴有冰雹", R.mipmap.ic_thundershower_hail_day);//雷阵雨伴有冰雹
        WEATHER_NAME_DES_ICON_DAY.put("冰雹", R.mipmap.ic_hail);//冰雹
        WEATHER_NAME_DES_ICON_DAY.put("冰针", R.mipmap.ic_needle_ice);//冰针
        WEATHER_NAME_DES_ICON_DAY.put("冰粒", R.mipmap.ic_icy);//冰粒
        WEATHER_NAME_DES_ICON_DAY.put("雨夹雪", R.mipmap.ic_sleet);//雨夹雪
        WEATHER_NAME_DES_ICON_DAY.put("小雨", R.mipmap.ic_light_rain);//小雨
        WEATHER_NAME_DES_ICON_DAY.put("中雨", R.mipmap.ic_rain);//中雨
        WEATHER_NAME_DES_ICON_DAY.put("大雨", R.mipmap.ic_heavy_rain);//大雨
        WEATHER_NAME_DES_ICON_DAY.put("暴雨", R.mipmap.ic_rainstorm);//暴雨
        WEATHER_NAME_DES_ICON_DAY.put("大暴雨", R.mipmap.ic_heavy_rainstorm);//大暴雨
        WEATHER_NAME_DES_ICON_DAY.put("特大暴雨", R.mipmap.ic_extreme_rainstorm);//特大暴雨
        WEATHER_NAME_DES_ICON_DAY.put("小雪", R.mipmap.ic_light_snow);//小雪
        WEATHER_NAME_DES_ICON_DAY.put("中雪", R.mipmap.ic_snow);//中雪
        WEATHER_NAME_DES_ICON_DAY.put("大雪", R.mipmap.ic_heavy_snow);//大雪
        WEATHER_NAME_DES_ICON_DAY.put("暴雪", R.mipmap.ic_blizzard);//暴雪
        WEATHER_NAME_DES_ICON_DAY.put("冻雨", R.mipmap.ic_freezing_rain);//冻雨
        WEATHER_NAME_DES_ICON_DAY.put("雪", R.mipmap.ic_snow);//雪
        WEATHER_NAME_DES_ICON_DAY.put("雨", R.mipmap.ic_rain);//雨
        WEATHER_NAME_DES_ICON_DAY.put("小到中雨", R.mipmap.ic_rain);//小到中雨
        WEATHER_NAME_DES_ICON_DAY.put("中到大雨", R.mipmap.ic_heavy_rain);//中到大雨
        WEATHER_NAME_DES_ICON_DAY.put("大到暴雨", R.mipmap.ic_rainstorm);//大到暴雨
        WEATHER_NAME_DES_ICON_DAY.put("小到中雪", R.mipmap.ic_sm_snow);//小到中雪
    }

    //空气质量和质量icon对应关系
    public static final HashMap<String, Integer> AIR_NAME_DES_ICON = new HashMap<>();

    static {
        AIR_NAME_DES_ICON.put("优", R.drawable.air_you_bg);// 优
        AIR_NAME_DES_ICON.put("良", R.drawable.air_lianghao_bg);//良
        AIR_NAME_DES_ICON.put("轻度污染", R.drawable.air_qingdu_bg);// 轻度污染
        AIR_NAME_DES_ICON.put("中度污染", R.drawable.air_zhongdu_bg);//中度污染
        AIR_NAME_DES_ICON.put("重度污染", R.drawable.air_zhongdu2_bg);//重度污染
        AIR_NAME_DES_ICON.put("严重污染", R.drawable.air_yanzhong_bg);//严重污染
        AIR_NAME_DES_ICON.put("爆表", R.drawable.air_baobiao_bg);//爆表
        AIR_NAME_DES_ICON.put("异常", R.drawable.air_yichang_bg);//异常
    }


    /**
     * 天气接口返回值对照天气信息说明
     */
    public static final HashMap<String, Integer> DESC2CODE = new HashMap<>();

    static {
        DESC2CODE.put("晴", 1);// 晴
        DESC2CODE.put("大部晴朗", 6);// 大部晴朗
        DESC2CODE.put("多云", 8);// 多云

        DESC2CODE.put("少云", 12);//少云
        DESC2CODE.put("阴", 13);//阴
        DESC2CODE.put("阵雨", 15);//阵雨
        DESC2CODE.put("局部阵雨", 20);//局部阵雨

        DESC2CODE.put("小阵雨", 22);//小阵雨
        DESC2CODE.put("强阵雨", 23);//强阵雨
        DESC2CODE.put("阵雪", 24);//阵雪
        DESC2CODE.put("小阵雪", 25);//小阵雪
        DESC2CODE.put("雾", 26);//雾
        DESC2CODE.put("冻雾", 28);//冻雾
        DESC2CODE.put("沙尘暴", 29);//沙尘暴
        DESC2CODE.put("浮尘", 30);//浮尘
        DESC2CODE.put("尘卷风", 31);//尘卷风
        DESC2CODE.put("扬沙", 32);//扬沙
        DESC2CODE.put("强沙尘暴", 33);//强沙尘暴
        DESC2CODE.put("霾", 34);//霾
        DESC2CODE.put("阴", 36);//阴
        DESC2CODE.put("雷阵雨", 37);//雷阵雨
        DESC2CODE.put("雷电", 42);//雷电
        DESC2CODE.put("雷暴", 43);//雷暴
        DESC2CODE.put("雷阵雨伴有冰雹", 44);//雷阵雨伴有冰雹
        DESC2CODE.put("冰雹", 46);//冰雹
        DESC2CODE.put("冰针", 47);//冰针
        DESC2CODE.put("冰粒", 48);//冰粒
        DESC2CODE.put("雨夹雪", 49);//雨夹雪
        DESC2CODE.put("小雨", 51);//小雨
        DESC2CODE.put("中雨", 53);//中雨
        DESC2CODE.put("大雨", 54);//大雨
        DESC2CODE.put("暴雨", 55);//暴雨
        DESC2CODE.put("大暴雨", 56);//大暴雨
        DESC2CODE.put("特大暴雨", 57);//特大暴雨
        DESC2CODE.put("小雪", 58);//小雪
        DESC2CODE.put("中雪", 60);//中雪
        DESC2CODE.put("大雪", 62);//大雪
        DESC2CODE.put("暴雪", 63);//暴雪


        DESC2CODE.put("冻雨", 64);//冻雨
        DESC2CODE.put("小雨", 66);//小雨
        DESC2CODE.put("中雨", 67);//中雨
        DESC2CODE.put("大雨", 68);//大雨
        DESC2CODE.put("大暴雨", 69);//大暴雨
        DESC2CODE.put("小雪", 73);//小雪
        DESC2CODE.put("大雪", 76);//大雪
        DESC2CODE.put("雪", 77);//雪
        DESC2CODE.put("雨", 78);//雨
        DESC2CODE.put("霾", 79);//霾
        DESC2CODE.put("多云", 81);//多云
        DESC2CODE.put("雾", 84);//雾
        DESC2CODE.put("阴", 85);//阴
        DESC2CODE.put("阵雨", 86);//阵雨
        DESC2CODE.put("雷阵雨", 90);//雷阵雨
        DESC2CODE.put("小到中雨", 91);//小到中雨
        DESC2CODE.put("中到大雨", 92);//中到大雨
        DESC2CODE.put("大到暴雨", 93);//大到暴雨
        DESC2CODE.put("小到中雪", 94);//小到中雪
    }

    public static final String SYSTEM_PROPERTY_DEVICE_THEME_MODE = "persist.car.night.mode.theme";

    public static final String  SETTING_UI_NIGHT_MODE= "settings_ui_night_mode";
    //系统设置 显示 主题设置 浅色、深色、自动三种
    public static final int UI_DAY_MODE = 0;
    public static final int UI_NIGHT_MODE = 1;
    public static final int UI_AUTO_MODE = 2;
}
