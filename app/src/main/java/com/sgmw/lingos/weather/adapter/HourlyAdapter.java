package com.sgmw.lingos.weather.adapter;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sgmw.entity.HourlyDataItem;
import com.sgmw.lingos.weather.R;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class HourlyAdapter extends RecyclerView.Adapter<HourlyAdapter.ViewHolder> {
    private static final String logTag = LOG_TAG + "-HourlyAdapter:";

    List<HourlyDataItem> hourlyDataItemList;

    public HourlyAdapter(List<HourlyDataItem> hourlyDataItems) {
        this.hourlyDataItemList = hourlyDataItems;
    }

    class ViewHolder extends RecyclerView.ViewHolder {

        TextView hourly_time;
        TextView hourly_time_now;
        TextView hourly_temp;
        ImageView hourly_weather;


        public ViewHolder(@NonNull @NotNull View itemView) {
            super(itemView);
            hourly_time = itemView.findViewById(R.id.tv_hourly_time);
            hourly_time_now = itemView.findViewById(R.id.tv_hourly_time_now);
            hourly_weather = itemView.findViewById(R.id.iv_hourly_weather);
            hourly_temp = itemView.findViewById(R.id.tv_hourly_temp);
        }

        public void bindHourlyDataItem(HourlyDataItem dataItem, int position) {
            if (position == 0) {
                hourly_time_now.setVisibility(View.VISIBLE);
                hourly_time.setVisibility(View.INVISIBLE);
            } else {
                hourly_time_now.setVisibility(View.INVISIBLE);
                hourly_time.setVisibility(View.VISIBLE);
            }
            hourly_temp.setText(dataItem.getTemp());
            hourly_time.setText(dataItem.getTime());
            hourly_weather.setImageResource(dataItem.getWeatherImageResId());
        }
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.hourly_24_item_layout, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

        HourlyDataItem hourlyDataItemH = hourlyDataItemList.get(position);

        holder.bindHourlyDataItem(hourlyDataItemH, position);

    }


    @Override
    public int getItemCount() {
        return hourlyDataItemList.size();
    }
}
