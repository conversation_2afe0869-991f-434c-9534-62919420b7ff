package com.sgmw.lingos.weather.utils;


import java.security.MessageDigest;
import java.util.TreeSet;

import timber.log.Timber;

public class SignatureUtils {

    // 网络请求签名
    private static final char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9','A', 'B', 'C', 'D', 'E', 'F'};

    public static String getSign(final boolean isDebug,
                                 final String vin,
                                 String... params){
        TreeSet<String> treeSet = new TreeSet();
        StringBuilder builder = new StringBuilder();
        for(String param : params){
            treeSet.add(param);
        }
        treeSet.add(vin);
        treeSet.add(isDebug ? Constants.API_KEY_TEST : Constants.API_KEY_NORMAL);
        for (String str : treeSet){
            builder.append(str);
        }
        return builder.toString();
    }

    public final static String MD5(String s) {
        try {
            byte[] btInput = s.getBytes();
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            //e.printStackTrace();
            Timber.e("Exception:MD5");
            return null;
        }
    }


}
