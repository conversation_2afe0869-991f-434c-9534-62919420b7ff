package com.sgmw.lingos.weather.server;



import com.sgmw.entity.ResponseWeatherBean;

import okhttp3.RequestBody;
import retrofit2.Call;//用于表示一个网络请求的调用
import retrofit2.http.Body;//将数据作为请求体发送到服务器
import retrofit2.http.Headers;//方法请求头
import retrofit2.http.POST;//用于请求http协议的方法

public interface ApiServer {
    @Headers("apiKey:AD3FE1315192093DD30E1978DED97FD7A15798D2918AE152A3E7E01D8073521A")
    @POST("getWeatherByDistrictId")
    Call<ResponseWeatherBean> getWeatherById(@Body RequestBody body);

    @Headers("apiKey:AD3FE1315192093DD30E1978DED97FD7A15798D2918AE152A3E7E01D8073521A")
    @POST("getWeatherByPosition")
    Call<ResponseWeatherBean> getWeatherByPosition(@Body RequestBody body);
}
