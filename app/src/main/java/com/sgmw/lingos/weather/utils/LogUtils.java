package com.sgmw.lingos.weather.utils;

import android.util.Log;

/**
 * 从systemUi 复制来的 日志管理打印
 *
 * <AUTHOR>
 */
public class LogUtils {
    private static final boolean DEBUGABLE = true;

    public static void v(String tag, String msg){
        if(DEBUGABLE){
            Log.v(Constants.LOG_TAG + tag, (msg == null ? "msg == null" : msg));
        }
    }

    public static void d(String tag, String msg){
        if(DEBUGABLE){
            Log.d(Constants.LOG_TAG + tag, (msg == null ? "msg == null" : msg));
        }
    }

    public static void i(String tag, String msg){
        if(DEBUGABLE){
            Log.i(Constants.LOG_TAG + tag, (msg == null ? "msg == null" : msg));
        }
    }

    public static void w(String tag, String msg){
        if(DEBUGABLE){
            Log.w(Constants.LOG_TAG + tag, (msg == null ? "msg == null" : msg));
        }
    }

    public static void e(String tag, String msg){
        if(DEBUGABLE){
            Log.e(Constants.LOG_TAG + tag, (msg == null ? "msg == null" : msg));
        }
    }

    public static void e(String tag, Throwable throwable){
        if(DEBUGABLE){
            Log.e(Constants.LOG_TAG + tag,"", throwable);
        }
    }
}
