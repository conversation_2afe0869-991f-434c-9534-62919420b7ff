package com.sgmw.lingos.weather;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.Manifest;
import android.app.Activity;
import android.app.Application;
import android.content.ComponentCallbacks;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.RemoteException;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.autoai.avs.crypto.client.CryptoManager;
import com.google.gson.Gson;
import com.sgmw.lingos.weather.helper.PrefUtils;
import com.sgmw.lingos.weather.manager.AccountValueContentObserver;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.receiver.RequestWeatherReceiver;
import com.sgmw.lingos.weather.receiver.TimeChangeReceiver;
import com.sgmw.lingos.weather.utils.CommonUtils;
import com.sgmw.lingos.weather.utils.LogUtils;
import com.sgmw.lingos.weather.utils.MyNetworkUtil;
import com.sgmw.lingos.weather.utils.SensorsDataManager;
import com.sgmw.lingos.weather.utils.WeatherDataUtil;
import com.sgmw.permissionsdk.PermissionManager;
import com.sgmw.voice_engine.manager.adapter.VrSystemSettingManager;
import com.sgmw.voice_engine.manager.sdk.SdkManager;

import org.json.JSONException;
import org.json.JSONObject;

import timber.log.Timber;

public class WeatherApp extends Application implements ComponentCallbacks, AccountValueContentObserver.IAccountInfoListener {
    private final String logTag = LOG_TAG + "-WeatherApp:";
    public Gson gson = new Gson();
    public static Application sInstance;
    private String TAG = "WeatherApp";


    /**
     *  执行成功
     */
    private final static int SUCCESS = 1;
    /**
     *  执行失败
     */
    private final static int FAIL = 0;
    /**
     * 已是当前状态
     */
    private final static int CURRENT_STATE = 2;
    /**
     * 定位权限未打开
     */
    private final static int NO_PERMISSION = 3;

    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;

        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                CommonUtils.getInstance().setTopActivity(activity);
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                if (activity == CommonUtils.getInstance().getTopActivity()) {
                    CommonUtils.getInstance().setTopActivity(null);
                }
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {

            }
        });
        PrefUtils.getInstance().initSpUtils(this, "WeatherInfo");
        Timber.plant(new Timber.DebugTree());
        Timber.tag(LOG_TAG);
        Timber.i("%sonCreate", logTag);
        MyNetworkUtil.getInstance().init(this);
        CryptoManager.INSTANCE.init(this);
        
        // 动态注册广播接收器，避免 Android 8.0+ 的限制
        IntentFilter filter = new IntentFilter();
        filter.addAction("com.sgmw.weather.request_weather");
        getApplicationContext().registerReceiver(new RequestWeatherReceiver(), filter);

        IntentFilter timeFilter = new IntentFilter();
        timeFilter.addAction(Intent.ACTION_TIME_TICK);
        getApplicationContext().registerReceiver(new TimeChangeReceiver(), timeFilter);

        SdkManager.getInstance().init(getApplicationContext());
        VrSystemSettingManager.getInstance().setISyStemClient(new VrSystemSettingManager.ISYSClient() {

            @Override
            public void openSystemSetting(boolean b) {

            }

            @Override
            public void backHome() {
            }

            @Override
            public void openBlueThoothUI() {
            }

            @Override
            public void setBluetoothState(boolean b) {

            }

            @Override
            public void openWifiUI() {
            }

            @Override
            public void setWifiState(boolean b) {

            }

            @Override
            public void openHotUI() {
            }

            @Override
            public void setHotspotState(String extra) {

            }

            @Override
            public void switchLargeModel() {

            }

            @Override
            public void setHotpotMode(String mode) {

            }

            @Override
            public void openSystemShowUi() {
            }

            @Override
            public void setBrightnessMode(VrSystemSettingManager.BrightnessMode brightnessMode, boolean b) {

            }

            @Override
            public void onVolumeControl(String s) {

            }

            @Override
            public void openVolumeUI() {

            }

            @Override
            public void openSoundEffectsUI() {

            }

            @Override
            public void setVolumeMuteState(String s) {

            }

            @Override
            public void openVoiceUi() {
            }

            @Override
            public void openSkillCenterUI() {

            }

            @Override
            public void openSystemUI() {
            }

            @Override
            public void openDrivingSettings() {

            }

            @Override
            public void openCloseWeather(boolean open) {
                Log.i(TAG, "148:openCloseWeather:  " + open);
                if (open) {
                    if (CommonUtils.getInstance().getTopActivity() != null) {
                        doneResult(true, CURRENT_STATE);
                        Log.i(TAG, "202:openCloseWeather: 应用已经打开 ");
                        return;
                    }
                    Intent intent = new Intent();
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.putExtra("voice", true);
                    intent.setClassName(getPackageName(), MainActivity.class.getName());
                    startActivity(intent);
                    int result = SUCCESS;
                    try {
                        boolean getPermission = PermissionManager.getInstance().checkPermission(getPackageName(), Manifest.permission_group.LOCATION);
                        LogUtils.i(TAG, "openCloseWeather: getPermission:"+getPermission);
                        //语音增加没有权限 时的情况处理
                        if (!getPermission) {
                            result = NO_PERMISSION;
                        }
                    } catch (RemoteException e) {
                        throw new RuntimeException(e);
                    }
                    doneResult(true, result);
                    Log.i(TAG, "211:openCloseWeather: 应用打开成功 ");
                } else {
                    if (CommonUtils.getInstance().getTopActivity()  != null) {
                        boolean move2Back = CommonUtils.getInstance().getTopActivity().moveTaskToBack(true);
                        doneResult(false, move2Back ? SUCCESS : FAIL);
                        Log.i(TAG, "216:openCloseWeather: 关闭应用 " + move2Back);
                    } else {
                        doneResult(false, CURRENT_STATE);
                        Log.i(TAG, "219:openCloseWeather: 已经关闭 ");
                    }
                }
            }

            @Override
            public void openReminderSettings() {

            }

            @Override
            public void openVoiceSettings() {

            }

            @Override
            public void getTTSPlayStatus(boolean b) {

            }

            @Override
            public void ttsPlayError(String s, String s1) {

            }

            @Override
            public void ttsPlayInterrupted(String s, String s1) {

            }

            @Override
            public void ttsPlayBegin(String s, String s1) {

            }

            @Override
            public void ttsPlayCompleted(String s, String s1) {

            }

            @Override
            public void openCloseAutoBrightnessAdjustment(boolean b) {

            }

            @Override
            public void setFontSize(String s) {

            }

            @Override
            public void adjustmentFontSize(String s) {

            }

            @Override
            public void setTimeFormat(String s) {

            }

            @Override
            public void openCloseReduceMediaDuringNavi(boolean b) {

            }

            @Override
            public void setSoundEffect(String s) {

            }

            @Override
            public void setFieldMode(String s) {

            }

            @Override
            public void openFourAwakeVocalRange(String extra) {

            }

            @Override
            public void closeFourAwakeVocalRange(String extra) {

            }

            @Override
            public void setFourAwakeVocalRange(String extra) {

            }

            @Override
            public void openCloseTwoWordWakeUp(boolean b) {

            }

            @Override
            public void openCloseNewsBroadcast(boolean b) {

            }

            @Override
            public void openCloseDestinationMemory(boolean b) {

            }

            @Override
            public void openCloseNoWakeUpRequired(boolean b) {

            }

            @Override
            public void setContinuouslySpeaking(String s) {

            }

            @Override
            public void setContinuouslySpeakingTime(String s) {

            }

            @Override
            public void setBroadcastTone(String s) {

            }

            @Override
            public void setRandomBroadcastTone() {

            }

            @Override
            public void setBroadcastMode(String s) {

            }

            @Override
            public void setOtherBroadcastMode() {

            }

            @Override
            public void setBroadcastVoice(String s) {

            }

            @Override
            public void openCloseThoughtfulReminder(boolean b) {

            }

            @Override
            public void openCloseSafetyBeltWarning(boolean b) {

            }

            @Override
            public void openCloseLowBatteryWarning(boolean b) {

            }

            @Override
            public void openCloseLowOilQuantityWarning(boolean b) {

            }

            @Override
            public void openCloseFatigueDrivingWarning(boolean b) {

            }

            @Override
            public void openCloseLowTrafficWarning(boolean b) {

            }

            @Override
            public void openSystemSettings() {

            }

            @Override
            public void openClosePermissionManagementUI(boolean b) {

            }

            @Override
            public void openCloseMicPermission(boolean b) {

            }

            @Override
            public void openClosePositioningPermission(boolean b) {

            }

            @Override
            public void setNaviPositioningPermissionTime(String s) {

            }

            @Override
            public void setWeatherPositioningPermissionTime(String s) {

            }

            @Override
            public void setVoiceMicPermissionTime(String s) {

            }

            @Override
            public void setPhoneMicPermissionTime(String s) {

            }

            @Override
            public void setKSongMicPermissionTime(String onOff) {

            }

            @Override
            public void openCloseStorageManagementUI(boolean b) {

            }

            @Override
            public void openCloseReduceMediaVolumeWhenOpeningDoorSwitch(boolean open) {

            }

            @Override
            public void setDeepThinking(boolean open) {

            }

            @Override
            public void restoreCarFactorySettings(String s) {

            }

            @Override
            public void restartVehicleCommunicationModule(String s) {

            }

            @Override
            public void setThemeSettings(String onOff) {

            }

        });
        SensorsDataManager.initSensorsDataSDK(this);
        WeatherManger.getInstance(WeatherApp.getInstance());
        AccountValueContentObserver.getInstance(WeatherApp.getInstance(), new Handler(WeatherApp.getInstance().getMainLooper()), this);
        WeatherDataUtil.initData();
    }


    /**
     *
     * @param openOrClose 打开还是关闭
     * @param result 1 执行成功  0 执行失败 2 已是当前状态  3 定位权限未打开
     */
    private void doneResult(boolean openOrClose, int result) {
        JSONObject object = new JSONObject();
        try {
            object.put("id", "sgmw.focus.setting.app.receiving");
            object.put("e_app_name", "天气");
            object.put("e_device_onoff", openOrClose ? "on" : "off");
            object.put("ret", result);
            String jsonString = object.toString();
            LogUtils.i(logTag,"requestTtsWithId:"+jsonString);
            SdkManager.getInstance().requestTtsWithId((String) object.get("id"), jsonString);
        } catch (JSONException e) {
            Log.e(TAG, "203:openCloseWeather:  ", e);
        }
    }

    /**
     * 获得当前app运行的Application
     */
    public static Application getInstance() {
        if (sInstance == null) {
            throw new NullPointerException("please inherit BaseApplication or call setApplication.");
        }
        return sInstance;
    }

    /**
     * 判断当前是否为天气页面
     * @return true 是  false 不是
     */
    public static boolean isWeatherApp() {
        return CommonUtils.getInstance().getTopActivity()!=null;
    }

    @Override
    public void onLowMemory() {
        Timber.e("%sonLowMemory", logTag);
        excuseKillMemory();
        super.onLowMemory();
    }

    @Override
    public void onTrimMemory(int level) {
        Timber.e("%sonTrimMemory", logTag);
        excuseKillMemory();
        super.onTrimMemory(level);
    }

    public void excuseKillMemory() {
        if (!CommonUtils.getInstance().isTopPackage() && CommonUtils.getRunningMemory(getApplicationContext()) > 180) {//根据行动项一确定自己模块的阈值
            Log.i(TAG, "281 excuseKillMemory: 低内存");
        }
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        // 程序终止的时候执行
        Timber.e("onTerminate");

        // 清理WeatherManger资源
        try {
            WeatherManger weatherManger = WeatherManger.getInstance(this);
            if (weatherManger != null) {
                weatherManger.cleanup();
                Timber.i("WeatherManger cleanup completed in onTerminate");
            }
        } catch (Exception e) {
            Timber.e("Error during WeatherManger cleanup in onTerminate: " + e.getMessage());
        }
    }

    @Override
    public void onUserInfoChange(String userInfo) {
        SensorsDataManager.login(userInfo);
    }


}