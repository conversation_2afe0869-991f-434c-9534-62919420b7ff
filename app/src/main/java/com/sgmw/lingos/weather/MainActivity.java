package com.sgmw.lingos.weather;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LIVE_INDEX;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_CITY;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_LOCATION_WINFO;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_UPDATE_TIME;
import static com.sgmw.lingos.weather.utils.ScreenUtil.toggleFullScreen;

import android.Manifest;
import android.annotation.SuppressLint;
import android.car.bus.SGMWBus;
import android.car.bus.SGMWBusEvent;
import android.car.bus.SGMWBusEventType;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.database.ContentObserver;
import android.graphics.drawable.AnimationDrawable;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.RemoteException;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.gson.reflect.TypeToken;
import com.sgmw.entity.Forecast;
import com.sgmw.entity.Hourly;
import com.sgmw.entity.HourlyDataItem;
import com.sgmw.entity.LiveIndexExt;
import com.sgmw.entity.WeatherData;
import com.sgmw.lingos.weather.adapter.HourlyAdapter;
import com.sgmw.lingos.weather.adapter.WeekAdapter;
import com.sgmw.lingos.weather.databinding.ActivityMainBinding;
import com.sgmw.lingos.weather.helper.PrefUtils;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.model.WeatherViewModel;
import com.sgmw.lingos.weather.server.ServiceConnector;
import com.sgmw.lingos.weather.utils.Constants;
import com.sgmw.lingos.weather.utils.GsonUtil;
import com.sgmw.lingos.weather.utils.LogUtils;
import com.sgmw.lingos.weather.utils.MyNetworkUtil;
import com.sgmw.lingos.weather.utils.ScreenUtil;
import com.sgmw.lingos.weather.utils.SensorsDataManager;
import com.sgmw.lingos.weather.utils.WeatherDataUtil;
import com.sgmw.permissionsdk.IHandleDialogListener;
import com.sgmw.permissionsdk.PermissionChangeListener;
import com.sgmw.permissionsdk.PermissionManager;
import com.sgmw.permissionsdk.bean.PermissionApp;
import com.sgmw.permissionsdk.bean.PermissionGroups;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import timber.log.Timber;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {
    private String TAG = "MainActivity";
    private static final String logTag = LOG_TAG + "-MainActivity:";
    ActivityMainBinding activityMainBinding;

    public static final float[] LARGE_SCREEN_SCALE = {1.065F, 1.0F, 0.87F};
    public static final float[] SMALL_SCREEN_SCALE = {1.3F, 1.15F, 1.0F};

    /**
     * 24小时天气适配器
     */
    private HourlyAdapter mHourlyAdapter;
    private WeekAdapter mWeekAdapter;

    //天气view model
    public WeatherViewModel viewModel;
    /**
     * 24小时天气数据list
     */
    private List<HourlyDataItem> hourlyDataItemList;
    private List<Forecast> weekDataItemList;

    AnimationDrawable animationDrawable;//加载中动画
    private Handler workHandler;//工作handler
    private static final int MSG_RELOAD_TIMEOUT = 109;
    private static final int MSG_RELOAD_START = 108;
    private boolean isNight = false;
    private static final String HOURS_12 = "12";
    private static final String HOURS_24 = "24";
    private static final String TIME_12 = "a hh:mm";
    private static final String TIME_24 = "HH:mm";
    private TimeSetObserver timeSetObserver;
    private static final int APP_LIST_TYPE = 1;
    private static final int LAUNCHER_TYPE = 0;
    private static final int VOICE_TYPE = 2;
    int openType = 0;
    long useTime = 0;
    private IHandleDialogListener iHandleDialogListener;
    private boolean isAtTop = false;
    private boolean isAtBottom = false;

    private ContentObserver mSettingsObserver;
    private static final String LINGOS_AGREEMENT = "lingos_agree_agreement";
    private String agreement;


    private PermissionChangeListener permissionChangeListener = new PermissionChangeListener() {
        @Override
        public void permissionChanges(String data) {
            LogUtils.i(TAG, "PermissionChangeListener " + data);
            try {
                List<PermissionApp> permissionApps = GsonUtil.fromJson(data, new TypeToken<List<PermissionApp>>() {
                }.getType());
                for (PermissionApp app : permissionApps) {
                    if (TextUtils.equals(app.packageName, getPackageName())) {
                        for (PermissionGroups groups : app.permissionGroups) {
                            if (TextUtils.equals(groups.groupName, Manifest.permission_group.LOCATION)) {
                                if (!groups.isGrant) {
                                    finish();
                                    LogUtils.i(TAG, "PermissionChangeListener kill");
                                }
                                return;
                            }
                        }
                    }
                }
            } catch (Exception e) {
                LogUtils.e(TAG, "162 permissionChanges: " + e.getMessage());
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(null);
        activityMainBinding = DataBindingUtil.setContentView(this, R.layout.activity_main);
        viewModel = new ViewModelProvider(this, new ViewModelProvider.AndroidViewModelFactory(this.getApplication())).get(WeatherViewModel.class);
        activityMainBinding.setLifecycleOwner(this);
        activityMainBinding.setViewModel(viewModel);

        //导航栏状态栏状态设置
        toggleFullScreen(this);
        workHandler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(@NonNull Message msg) {
                if (msg.what == MSG_RELOAD_TIMEOUT) {
                    hideBaseLoading(false);
                } else if (msg.what == MSG_RELOAD_START) {
                    try {
                        if (viewModel != null) {
                            viewModel.reload(getApplication());
                        }
                    } catch (RemoteException e) {
                        Timber.e(logTag + "viewModel.reload() error : " + e.getMessage());
                    }
                }
            }
        };
        animationDrawable = (AnimationDrawable) activityMainBinding.ivLoadingData.getDrawable();
        //绑定天气服务
        WeatherManger.getInstance(getApplicationContext()).setCallback(ServiceConnector.getInstance());
        //欢迎页面监听
        registerLingOSAgreement();
        registerPermission();

        initData();
        initView();
        initObserve();
        getSysSettingInfo();
        viewModel.refreshUpdateTime();
        viewModel.refreshTimeLive(getApplicationContext());
        viewModel.refreshCity();
        viewModel.refreshLiveIndex();

        showWeekWeather(View.GONE);
//        try {
//            boolean getPermission = PermissionManager.getInstance().checkPermission(getPackageName(), Manifest.permission_group.LOCATION);
//            if (getPermission) {
//                initWeatherData();
//            }
//        } catch (RemoteException e) {
//            LogUtils.e(TAG, e.getMessage());
//        }
        openType(getIntent());
        LogUtils.i(TAG, "onCreate-----------");
        iHandleDialogListener = new IHandleDialogListener() {
            @Override
            public void onResult(int code) {
                LogUtils.i(TAG, "460:onResult:  " + code);
                if (code != 0) {
                    finish();
                } else {
                    startActivity(new Intent(WeatherApp.getInstance(), MainActivity.class));
                    runOnUiThread(() -> {
                        initWeatherData();
                    });
                }
            }
        };
    }

    private void registerLingOSAgreement() {
        LogUtils.i(TAG, "registerLingOSAgreement-----------");
        mSettingsObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
            @Override
            public void onChange(boolean selfChange, @Nullable Uri uri) {
                super.onChange(selfChange, uri);
                agreement = Settings.Global.getString(getApplicationContext().getContentResolver(), LINGOS_AGREEMENT);
                LogUtils.i(TAG, "LINGOS_AGREEMENT agreement = " + agreement);
                //同意 1 ; 其他值不同意
                if (TextUtils.equals(agreement, "1")) {
                    checkPermission();
                } else {
                    finish();
                }
            }
        };
        getContentResolver().registerContentObserver(Settings.Global.getUriFor(LINGOS_AGREEMENT), false, mSettingsObserver);
    }


    private void openType(Intent intent) {
        String fromByAppList = intent.getStringExtra("source");
        boolean fromByVoice = intent.getBooleanExtra("voice", false);
        if (TextUtils.isEmpty(fromByAppList)) {
            if (fromByVoice) {
                //语音打开
                openType = VOICE_TYPE;
            } else {
                //桌面打开
                openType = LAUNCHER_TYPE;
            }
        } else {
            //applist打开
            openType = APP_LIST_TYPE;
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        openType(intent);
    }

    private void registerPermission() {
        try {
            PermissionManager.getInstance().registerChangeListener(permissionChangeListener);
        } catch (RemoteException e) {
            LogUtils.e(TAG, "167 registerPermission: " + e.getMessage());
        }
    }

    private void unregisterPermission() {
        try {
            PermissionManager.getInstance().unregisterChangeListener(permissionChangeListener);
        } catch (RemoteException e) {
            LogUtils.e(TAG, "167 registerPermission: " + e.getMessage());
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        recreate();
    }

    @Override
    protected void onPause() {
        super.onPause();
        try {
            Thread.sleep(40);
        } catch (InterruptedException e) {
            LogUtils.i(TAG, "152:onPause: 主动暂停50ms launcher点击天气,在点击应用列表出现先出现桌面在出现应用列表问题 ");
            Thread.currentThread().interrupted();
        }
        //使用时间
        useTime = (SystemClock.elapsedRealtime() - useTime) / 1000;
        SensorsDataManager.useDuring(useTime + "");
        LogUtils.i(TAG, "210 onPause: " + useTime);
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.i(TAG, "onResume-----------onResume");
        //从applist进入
        useTime = SystemClock.elapsedRealtime();

        //隐私协议弹窗 同意 1 ; 其他值不同意
        agreement = Settings.Global.getString(getContentResolver(), LINGOS_AGREEMENT);
        LogUtils.i(TAG, "LINGOS_AGREEMENT agreement = " + agreement);
        if (TextUtils.equals(agreement, "1")) {
            checkPermission();
        } else {
            showWelcomeDialog();
        }

//        initSevenDayView();
    }

    //展示弹窗
    public void showWelcomeDialog() {
        SGMWBus sGMWBus = new SGMWBus(this);
        SGMWBusEvent sgmwBusEvent = new SGMWBusEvent();
        sgmwBusEvent.mEventType = SGMWBusEventType.EVENT_LINGOS_SHOW;
        sGMWBus.publish(sgmwBusEvent);
    }

//    private SpaceItemDecoration weekItemDecoration;

    /**
     * 调整未来天气的item 间距 切换字体大小时
     */
    private void initSevenDayView() {
//        float aFloat = Settings.System.getFloat(this.getContentResolver(), Settings.System.FONT_SCALE, 1.0f);
//        LogUtils.i(logTag, aFloat + "");
//        weekItemDecoration = new SpaceItemDecoration(34);
        //处理大屏幕的情况， 小屏幕 字体最小也不会有空缺的现象，也不能再挤了
//        if (isLargeScreen(this)) {
//            LogUtils.i(TAG, "isLargeScreen:true");
//            if (aFloat == LARGE_SCREEN_SCALE[2]) {
//                //小字体时
//                weekItemDecoration = new SpaceItemDecoration(51);
//            } else if (aFloat == LARGE_SCREEN_SCALE[1]) {
//                weekItemDecoration = new SpaceItemDecoration(34);
//            } else if (aFloat == LARGE_SCREEN_SCALE[0]) {
//                weekItemDecoration = new SpaceItemDecoration(34);
//            }
//
//            int count = activityMainBinding.rv7dayWeatherList.getItemDecorationCount();
//            for (int i = 0; i < count; i++) {
//                activityMainBinding.rv7dayWeatherList.removeItemDecorationAt(0);
//            }
//            activityMainBinding.rv7dayWeatherList.addItemDecoration(weekItemDecoration);
//        }
    }

    private boolean isLargeScreen(Context context) {
        if (context == null) return true;
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        Display display = windowManager.getDefaultDisplay();
        DisplayMetrics displayMetrics = new DisplayMetrics();
        display.getRealMetrics(displayMetrics);
        int width = displayMetrics.widthPixels;
        int height = displayMetrics.heightPixels;
        return TextUtils.equals((width + "*" + height), "1920*1080");
    }

    /**
     * 初始化加载
     */
    private void initWeatherData() {
        boolean connected = MyNetworkUtil.getInstance().isConnected();
        Timber.i(logTag + "onResume called");
        WeatherData value = viewModel.getWeatherData().getValue();
        LogUtils.i(TAG, String.valueOf(value));
        if (!connected) {
            //2025-3-3 去掉重复提示
            //if (WeatherApp.isWeatherApp()) {
            //SGMWToast.makeToast(WeatherApp.getInstance(), WeatherApp.getInstance().getString(R.string.get_data_fail)).show();
            //}
            WeatherManger.getInstance(this).getNavAreaInfo();
            if (value == null) {
                String weatherStr = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_WINFO);
                LogUtils.i(TAG, "weatherStr == " + weatherStr);
                if (!TextUtils.isEmpty(weatherStr)) {
                    WeatherData weatherData = GsonUtil.fromJson(weatherStr, WeatherData.class);
                    if (weatherData != null) {
                        if (PrefUtils.getInstance() != null) {
                            String updateTime = PrefUtils.getInstance().getString(SP_KEY_LAST_UPDATE_TIME);
                            Timber.i(logTag + "initView updateTime = " + updateTime);
                            if (!TextUtils.isEmpty(updateTime)) {
                                activityMainBinding.tvUpdateTime.setText(updateTime);
                            }
                            String timeStyle = Settings.System.getString(getApplicationContext().getContentResolver(), Settings.System.TIME_12_24);
                            if (HOURS_12.equals(timeStyle)) {
                                initTimeFormat(false);
                                viewModel.mTimeLiveData.postValue(false);
                            } else if (HOURS_24.equals(timeStyle)) {
                                initTimeFormat(true);
                                viewModel.mTimeLiveData.postValue(true);
                            }
                            String sLastLocationCity = PrefUtils.getInstance().getString(SP_KEY_LAST_LOCATION_CITY);
                            Timber.i(logTag + "initView sLastLocationCity = " + sLastLocationCity);
                            if (!TextUtils.isEmpty(sLastLocationCity)) {
                                activityMainBinding.tvLocation.setText(sLastLocationCity);
                            }
                            String liveIndex = PrefUtils.getInstance().getString(SP_KEY_LAST_LIVE_INDEX);
                            Timber.i(logTag + "initView liveIndex = " + liveIndex);
                            if (!TextUtils.isEmpty(liveIndex)) {
                                LiveIndexExt liveIndexExt1 = GsonUtil.fromJson(liveIndex, LiveIndexExt.class);
                                if (liveIndexExt1 != null) {
                                    initLiveIndexExtData(liveIndexExt1);
                                }
                            }
                        }
                        viewModel.getWeatherData().postValue(weatherData);
                    } else {
                        return;
                    }
                } else {
                    activityMainBinding.clNoData.setVisibility(View.VISIBLE);
                    activityMainBinding.clPageContent.setVisibility(View.GONE);
                    activityMainBinding.clLoadingData.setVisibility(View.GONE);
                    return;
                }
            }
            activityMainBinding.clPageContent.setVisibility(View.VISIBLE);
            activityMainBinding.clNoData.setVisibility(View.GONE);
            activityMainBinding.clLoadingData.setVisibility(View.GONE);
            return;
        }
        showBaseLoading();
    }

    @Override
    protected void onPostResume() {
        super.onPostResume();
        LogUtils.i(TAG, "249 onPostResume: " + openType);
        switch (openType) {
            case VOICE_TYPE:
                SensorsDataManager.enterWeather("首页", "语音唤醒");
                break;
            default:
                SensorsDataManager.enterWeather("all app页", "屏幕操作");
                break;
        }
    }

    /*
     * 初始化viewmodel数据监听
     * */
    @SuppressLint({"NotifyDataSetChanged", "ClickableViewAccessibility"})
    private void initObserve() {
        //定位城市textview
        viewModel.getCurrentCityName().observe(this, inputText -> {
            Timber.i(logTag + "getCurrentCityName observe:" + inputText);
            if (inputText != null) {
                activityMainBinding.tvLocation.setText(inputText);
            }
        });
        viewModel.updateTimeLiveData.observe(this, new Observer<String>() {
            @Override
            public void onChanged(String updateTime) {
                LogUtils.i(TAG, "165:onChanged:刷新时间异常需检查是否多线程导致异常  " + updateTime);
                activityMainBinding.tvUpdateTime.setText(updateTime);
            }
        });
        //天气信息
        viewModel.getWeatherData().observe(this, inputWeatherData -> {
            initWeatherView(inputWeatherData);
        });

        //生活指数
        viewModel.getLiveIndex().observe(this, inputLiveIndexExt -> {
            initLiveIndexExtData(inputLiveIndexExt);
        });

        //更新时间
        activityMainBinding.ivRefresh.setOnClickListener(this);
        activityMainBinding.tvNoData.setOnClickListener(this);
        activityMainBinding.tv24hWeather.setOnClickListener(this);
        activityMainBinding.tv7dayWeather.setOnClickListener(this);


        viewModel.getIsLoadingSuccess().observe(this, inputIsSuccess -> {
            Timber.i(logTag + "getIsLoadingSuccess observe : " + inputIsSuccess);
            if (inputIsSuccess) {
                hideLoading();
                //加载成功，删除超时消息
                if (workHandler.hasMessages(MSG_RELOAD_TIMEOUT)) {
                    workHandler.removeMessages(MSG_RELOAD_TIMEOUT);
                }
                hideBaseLoading(true);
                //暂时注释掉，更新时间不应该在这里， 这个方法只有进入才会调用，时间会有问题， 统一用 updateTimeLiveData 来控制
//                String format = Boolean.TRUE.equals(viewModel.getTimeLevelData().getValue()) ? TIME_24 : TIME_12;
//                // 获取当前时间
//                Calendar calendar = Calendar.getInstance();
//                // 格式化时间为时分
//                SimpleDateFormat sdf = new SimpleDateFormat(format);
//                String formattedTime = sdf.format(calendar.getTime());
//                activityMainBinding.tvUpdateTime.setText(formatUpdateTime(format, formattedTime, calendar));
            } else {
                //activityMainBinding.ivRefreshAnimal.postDelayed(new Runnable() {
                activityMainBinding.ivRefresh.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        hideLoading();
                    }
                }, 3000);
            }
        });

        viewModel.mTimeLiveData.observe(this, is24model -> {
            initTimeFormat(is24model);
        });

        activityMainBinding.rv24hWeather.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent motionEvent) {
                switch (motionEvent.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        // 初始状态重置
                        isAtTop = false;
                        isAtBottom = false;
                        break;

                    case MotionEvent.ACTION_MOVE:
                        // 检查是否滑动到了顶部或底部
                        if (!view.canScrollHorizontally(-1)) {
                            if (!isAtTop) {
                                // 到达顶部
                                isAtTop = true;
                                isAtBottom = false;
                                sendBroadCast(Constants.SCROLL_TOP);
                            }
                        } else if (!view.canScrollHorizontally(1)) {
                            if (!isAtBottom) {
                                // 到达底部
                                isAtBottom = true;
                                isAtTop = false;
                                sendBroadCast(Constants.SCROLL_BOTTOM);
                            }
                        } else {
                            // 重置状态
                            isAtTop = false;
                            isAtBottom = false;
                        }
                        break;

                    case MotionEvent.ACTION_UP:
                        // 手指抬起时重置状态
                        isAtTop = false;
                        isAtBottom = false;
                        break;
                }
                return false;
            }
        });
    }

    private void sendBroadCast(int type) {
        Intent intent = new Intent(Constants.SCROLL_ACTION);
        intent.putExtra(Constants.SCROLL_STATUS, type);
        // 使用 LocalBroadcastManager 进行应用内通信，避免 Android 8.0+ 的限制
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
    }

    private void initTimeFormat(boolean is24model) {
        Timber.i(logTag + "mTimeLiveData observe : " + is24model);
        CharSequence tempTime = activityMainBinding.tvUpdateTime.getText();
        String format = is24model ? TIME_24 : TIME_12;
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        // 将原始时间格式进行解析
        SimpleDateFormat originalSdf = new SimpleDateFormat("HH:mm");
        Calendar originalTime = Calendar.getInstance();
        try {
            originalTime.setTime(originalSdf.parse(tempTime.toString()));
        } catch (ParseException e) {
            Timber.e(logTag + "mTimeLiveData observe originalTime format error: " + e);
        }
        // 将合并后的时间进行格式化
        String formattedOriginalTime = sdf.format(originalTime.getTime());
        String newUpdateTime = formatUpdateTime(format, formattedOriginalTime, originalTime);
        LogUtils.i(TAG, "tempTime: " + tempTime);
        LogUtils.i(TAG, "newUpdateTime: " + newUpdateTime);
        if (tempTime != null && tempTime.equals(newUpdateTime)) {
            return;
        }
        activityMainBinding.tvUpdateTime.setText(formatUpdateTime(format, formattedOriginalTime, originalTime));
    }

    private void initLiveIndexExtData(LiveIndexExt inputLiveIndexExt) {
        //洗车
        if (!isTextEmpty(inputLiveIndexExt.getCostWash())) {
            activityMainBinding.tvCarWash.setText(inputLiveIndexExt.getCostWash());
        } else {
            activityMainBinding.tvCarWash.setText(" --");
        }
        //紫外线
        if (!isTextEmpty(inputLiveIndexExt.getCostUltraviolet())) {
            activityMainBinding.tvUltravioletRays.setText(inputLiveIndexExt.getCostUltraviolet());
        } else {
            activityMainBinding.tvUltravioletRays.setText(" --");
        }
        //感冒
        if (!isTextEmpty(inputLiveIndexExt.getCostCold())) {
            activityMainBinding.tvCommonCold.setText(inputLiveIndexExt.getCostCold());
        } else {
            activityMainBinding.tvCommonCold.setText(" --");
        }
        //运动
        if (!isTextEmpty(inputLiveIndexExt.getCostSport())) {
            activityMainBinding.tvSport.setText(inputLiveIndexExt.getCostSport());
        } else {
            activityMainBinding.tvSport.setText(" --");
        }
        //穿衣
        if (!isTextEmpty(inputLiveIndexExt.getCostDress())) {
            activityMainBinding.tvDress.setText(inputLiveIndexExt.getCostDress());
        } else {
            activityMainBinding.tvDress.setText(" --");
        }
        //旅游
        if (!isTextEmpty(inputLiveIndexExt.getCostTourism())) {
            activityMainBinding.tvTravelIndex.setText(inputLiveIndexExt.getCostTourism());
        } else {
            activityMainBinding.tvTravelIndex.setText(" --");
        }
    }

    @SuppressLint("SetTextI18n")
    private void initWeatherView(WeatherData inputWeatherData) {
        if (inputWeatherData != null) {
            Timber.d("%s getWeatherData observe : %s", logTag, inputWeatherData.toString());

            //服务器当前时间
            long mCurrentTime = System.currentTimeMillis();
            Integer resId;
            if (mCurrentTime < inputWeatherData.getSunDown() && mCurrentTime > inputWeatherData.getSunUp()) {
                isNight = false;
            } else {
                isNight = true;
            }
            //当前温度
            if (!isTextEmpty(inputWeatherData.getTemperature())) {
                activityMainBinding.tvTempShow.setText(inputWeatherData.getTemperature());
            }
            //最高温
            if (!isTextEmpty(inputWeatherData.getTempDay())) {
                activityMainBinding.tvMaxtemp.setText(inputWeatherData.getTempDay());
            }
            //最低温
            if (!isTextEmpty(inputWeatherData.getTempNight())) {
                activityMainBinding.tvMintemp.setText(inputWeatherData.getTempNight());
            }
            //预警提示
            String mTips = inputWeatherData.getTips();
            if (mTips != null && !TextUtils.isEmpty(mTips)) {
                mTips = "" + mTips + "";
                activityMainBinding.tvEvaluation.setVisibility(View.VISIBLE);
                activityMainBinding.tvEvaluation.setText(mTips);
            } else {
                activityMainBinding.tvEvaluation.setVisibility(View.INVISIBLE);
            }

            //空气质量
            if (!isTextEmpty(inputWeatherData.getAqi())) {
                activityMainBinding.tvAirQuality.setText(inputWeatherData.getAqi()+" "+inputWeatherData.getAqiValue());
                Integer air_resId = Constants.AIR_NAME_DES_ICON.get(inputWeatherData.getAqi());
                activityMainBinding.ivAirQuality.setImageResource(air_resId != null ? air_resId : R.drawable.air_you_bg);
            }
            //空气质量指数值
            if (!isTextEmpty(inputWeatherData.getAqi())) {
                activityMainBinding.tvCommonAirQuality.setText(inputWeatherData.getAqi());
            }

            //24小时天气
            if (inputWeatherData.getHourly() != null && !inputWeatherData.getHourly().isEmpty()) {
                convertToHourlyDataItem(inputWeatherData.getHourly());
                HourlyDataItem hourlyDataItem = hourlyDataItemList.get(0);
                String weatherDesc = hourlyDataItem.getWeatherDesc();
                if (!isNight) {
                    resId = Constants.WEATHER_NAME_DES_ICON_DAY.get(weatherDesc);
                    activityMainBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_day);
                } else {
                    resId = Constants.WEATHER_DES_ICON_NIGHT.get(weatherDesc);
                    activityMainBinding.ivWeather.setImageResource(resId != null ? resId : R.mipmap.ic_sunny_dark);
                }
                //背景图片
                if (!isTextEmpty(weatherDesc)) {
                    Integer bg_resId = Constants.WEATHER_DES_IMAGE.get(weatherDesc);
                    activityMainBinding.clPageContent.setBackgroundResource(bg_resId != null ? bg_resId : R.mipmap.img_sunny);
                }
                LogUtils.i(TAG, "304:initObserve:  " + weatherDesc);
                LogUtils.i(TAG, "305:initObserve:  " + inputWeatherData.getText());
                //天气text
                if (!isTextEmpty(weatherDesc)) {
                    activityMainBinding.tvWeather.setText(weatherDesc);
                }
                Hourly currentHourly=inputWeatherData.getHourly().get(0);
                activityMainBinding.tvAirWind.setText(WeatherDataUtil.getWindName(currentHourly.getWindDir())+" "+WeatherDataUtil.getWindType(currentHourly.getWindSpeed()));
                activityMainBinding.tvAirWetness.setText(
                        getString(R.string.humidity)+" "+
                                (TextUtils.isEmpty(currentHourly.getHumidity())?getString(R.string.humidity):currentHourly.getHumidity())+
                                getString(R.string.def_percent));
                activityMainBinding.tvAirFeels.setText(getString(R.string.real_feel)+" "+
                                (TextUtils.isEmpty(currentHourly.getRealFeel())?getString(R.string.humidity):currentHourly.getRealFeel())+
                        getString(R.string.def_temp_unit));
                activityMainBinding.tvPressure.setText(currentHourly.getPressure());
                mHourlyAdapter.notifyDataSetChanged();

            }
            List<Forecast> forecast = inputWeatherData.getForecast();
            if (forecast != null) {
                LogUtils.i(TAG, "308 initObserve: " + forecast.size());
                if (forecast.size() > 1) {
                    weekDataItemList.clear();
                    List<Forecast> forecasts = forecast.subList(1, forecast.size());//第0个数据固定是昨天的 不做处理
                    LogUtils.i(TAG, "sublist.size == " + forecasts.size());
                    weekDataItemList.addAll(forecasts);
                    mWeekAdapter.notifyDataSetChanged();
                }
            }
        }
    }

    private String formatUpdateTime(String format, String formattedTime, Calendar calendar) {
        if (format.equals(TIME_12)) {
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            if (formattedTime.startsWith("12:") && hour == 0) {
                formattedTime = formattedTime.replace("12:", "00:");
            }
        }
        return formattedTime;
    }

    /**
     * 显示动画更新中
     */
    private void showLoading() {
        Timber.i(logTag + "showLoading called");
        //activityMainBinding.clRefreshTime.setVisibility(View.GONE);
        //activityMainBinding.clRefreshAnimal.setVisibility(View.VISIBLE);
        activityMainBinding.ivRefresh.startAnimation(ScreenUtil.getRotateAnimation());
        activityMainBinding.tvRefresh.setVisibility(View.VISIBLE);
        activityMainBinding.tvUpdateTip.setVisibility(View.INVISIBLE);
        activityMainBinding.tvUpdateTime.setVisibility(View.INVISIBLE);

    }

    private void hideLoading() {
        Timber.i(logTag + "hideLoading called");
        //activityMainBinding.clRefreshTime.setVisibility(View.VISIBLE);
        activityMainBinding.tvRefresh.setVisibility(View.INVISIBLE);
        activityMainBinding.tvUpdateTime.setVisibility(View.VISIBLE);
        activityMainBinding.tvUpdateTip.setVisibility(View.VISIBLE);
        Animation anim = activityMainBinding.ivRefresh.getAnimation();
        if (anim != null) {
            anim.cancel();
        }
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == activityMainBinding.ivRefresh.getId()) {
            showLoading();
            activityMainBinding.ivRefresh.postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (null != viewModel) {
                            viewModel.reload(MainActivity.this);
                        }
                    } catch (RemoteException e) {
                        Timber.e(logTag + "viewModel.reload() error : " + e.getMessage());
                    }
                }
            }, 2000);
        } else if (view.getId() == activityMainBinding.tvNoData.getId()) {
            showBaseLoading();
        } else if (view.getId() == activityMainBinding.tv7dayWeather.getId()) {
            //显示周天气
            showWeekWeather(View.VISIBLE);
            show24DayWeather(View.GONE);
        } else if (view.getId() == activityMainBinding.tv24hWeather.getId()) {
            //显示24小时天气
            show24DayWeather(View.VISIBLE);
            showWeekWeather(View.GONE);
        }
    }


    private void showBaseLoading() {
        Timber.i(logTag + "showBaseLoading called");
        activityMainBinding.clPageContent.setVisibility(View.GONE);
        activityMainBinding.clNoData.setVisibility(View.GONE);
        activityMainBinding.clLoadingData.setVisibility(View.VISIBLE);
        if (animationDrawable != null) {
            animationDrawable.start();
        }
        workHandler.sendEmptyMessage(MSG_RELOAD_START);
        workHandler.sendEmptyMessageDelayed(MSG_RELOAD_TIMEOUT, 8000);
    }

    private void hideBaseLoading(boolean isLoadingSuccess) {
        Timber.i(logTag + "hideBaseLoading called");
        if (isLoadingSuccess) {
            activityMainBinding.clPageContent.setVisibility(View.VISIBLE);
            activityMainBinding.clNoData.setVisibility(View.GONE);
        } else {
            activityMainBinding.clNoData.setVisibility(View.VISIBLE);
            activityMainBinding.clPageContent.setVisibility(View.GONE);
        }
        activityMainBinding.clLoadingData.setVisibility(View.GONE);
        if (animationDrawable != null) {
            animationDrawable.stop();
        }

    }

    private boolean isTextEmpty(String inText) {
        if (inText != null && !TextUtils.isEmpty(inText)) {
            return false;
        } else {
            return true;
        }
    }

    /*Hourly转换为HourlyDataItem list*/
    private void convertToHourlyDataItem(List<Hourly> list) {
        if (list == null || list.size() <= 0) {
            Timber.i("%s lsit is null", logTag);
            return;
        }
        hourlyDataItemList.clear();
        for (int i = 0; i < list.size(); i++) {
            Hourly hourly = list.get(i);
            String con_temp = hourly.getTemp();
            con_temp = con_temp + "°C";
            String con_hour = hourly.getHour();
            if (con_hour.length() < 2) {
                con_hour = "0" + con_hour + ":00";
            } else {
                con_hour = con_hour + ":00";
            }
            String hourly_str = hourly.getCondition();
            Map<String, Integer> weatherNameDesIcon;
            Timber.i("%s hourly date : %s , hour : %s , is night : %s", logTag, hourly.getDate(), hourly.getHour(), hourly.isIconIsNight());
            if (!hourly.isIconIsNight()) {
                weatherNameDesIcon = Constants.WEATHER_NAME_DES_ICON_DAY;
            } else {
                weatherNameDesIcon = Constants.WEATHER_NAME_DES_ICON_NIGHT;
            }
            int con_weatherImageResId;
            if (hourly_str != null && weatherNameDesIcon.containsKey(hourly_str)) {
                Integer temp_hourly_str = weatherNameDesIcon.get(hourly_str);
                con_weatherImageResId = temp_hourly_str == null ? R.mipmap.ic_sunny : temp_hourly_str;
            } else {
                con_weatherImageResId = R.mipmap.ic_sunny;
            }
            hourlyDataItemList.add(new HourlyDataItem(con_hour, con_temp, con_weatherImageResId, hourly_str));
        }
    }

    /*
     * 打开天气时需请求位置权限
     * */
    private void checkPermission() {
        LogUtils.i(TAG, "453:checkPermission:  ");
        try {
            boolean getPermission = PermissionManager.getInstance().checkPermission(getPackageName(), Manifest.permission_group.LOCATION);
            if (!getPermission) {
                try {
                    LogUtils.i(TAG, "455:checkPermission:  ");
                    PermissionManager.getInstance().showPermissionDialog("com.sgmw.lingos.weather", Manifest.permission_group.LOCATION, "开启天气定位权限", "定位权限未开启，天气暂不可用", iHandleDialogListener);
                    moveTaskToBack(true);
                    LogUtils.i(TAG, "456:checkPermission:  ");
                } catch (RemoteException e) {
                    LogUtils.e(TAG, "462:checkPermission:  " + e.getMessage());
                }
            } else {
                initWeatherData();
            }
        } catch (RemoteException e) {
            LogUtils.e(TAG, "502 checkPermission: " + e.getMessage());
        }

    }

    private void initView() {
        //24小时布局recycleView
        mHourlyAdapter = new HourlyAdapter(hourlyDataItemList);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        layoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        activityMainBinding.rv24hWeather.setLayoutManager(layoutManager);
        activityMainBinding.rv24hWeather.setAdapter(mHourlyAdapter);

        mWeekAdapter = new WeekAdapter(weekDataItemList);
        LinearLayoutManager weekLayoutManager = new LinearLayoutManager(this);
        weekLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
        activityMainBinding.rv7dayWeatherList.setLayoutManager(weekLayoutManager);
        activityMainBinding.rv7dayWeatherList.setAdapter(mWeekAdapter);

        //weekItemDecoration = new SpaceItemDecoration(34);
        //activityMainBinding.rv7dayWeatherList.addItemDecoration(weekItemDecoration);

        //SpaceItemDecoration itemDecoration = new SpaceItemDecoration(48);
        //activityMainBinding.rv24hWeather.addItemDecoration(itemDecoration);
    }

    /*recycleview 当前假数据*/
    private void initData() {
        hourlyDataItemList = new ArrayList<>();
        HourlyDataItem hourlyDataItem = new HourlyDataItem("12:00", "24°C", R.mipmap.ic_cloudy, "晴");
        for (int i = 0; i < 24; i++) {
            hourlyDataItemList.add(hourlyDataItem);
        }

        weekDataItemList = new ArrayList<>();
        Forecast weekData = new Forecast("35", "25", "晴", "11-1");
        for (int i = 0; i < 8; i++) {
            weekDataItemList.add(weekData);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Timber.i("%s onDestroy called", logTag);
        if (animationDrawable != null) {
            animationDrawable.stop();
            // 释放资源
            animationDrawable = null;
        }
        if (workHandler != null) {
            if (workHandler.hasMessages(MSG_RELOAD_TIMEOUT)) {
                workHandler.removeMessages(MSG_RELOAD_TIMEOUT);
            }
            if (workHandler.hasMessages(MSG_RELOAD_START)) {
                workHandler.removeMessages(MSG_RELOAD_START);
            }
            workHandler.removeCallbacksAndMessages(null);
            workHandler = null;
        }
        if (hourlyDataItemList != null) {
            hourlyDataItemList.clear();
            hourlyDataItemList = null;
        }

        if (viewModel != null) {
            viewModel.setIsLoadingSuccess(false);
            viewModel = null;
        }
        if (timeSetObserver != null) {
            getContentResolver().unregisterContentObserver(timeSetObserver);
            timeSetObserver = null;
        }

        if (mSettingsObserver != null) {
            getContentResolver().unregisterContentObserver(mSettingsObserver);
        }

        unregisterPermission();
        permissionChangeListener = null;
        iHandleDialogListener = null;
    }


    /*
     * 获取系统设置时间格式 12小时制 & 24小时制
     * */
    public void getSysSettingInfo() {
        timeSetObserver = new TimeSetObserver(new Handler(Looper.getMainLooper()));
        getContentResolver().registerContentObserver(Settings.System.getUriFor(Settings.System.TIME_12_24), false, timeSetObserver);
    }

    public class TimeSetObserver extends ContentObserver {
        public TimeSetObserver(Handler handler) {
            super(handler);
        }

        @Override
        public void onChange(boolean selfChange) {
            LogUtils.i(TAG, "609:onChange:  ");
            super.onChange(selfChange);
            if (viewModel == null) {
                return;
            }
            String timeStyle = Settings.System.getString(getApplicationContext().getContentResolver(), Settings.System.TIME_12_24);
            Timber.i(logTag + "mTimeLiveData onChange = " + timeStyle);
            if (HOURS_12.equals(timeStyle)) {
                viewModel.mTimeLiveData.postValue(false);
            } else if (HOURS_24.equals(timeStyle)) {
                viewModel.mTimeLiveData.postValue(true);
            }
        }
    }

    private void showWeekWeather(int visibility) {
        if (visibility == View.VISIBLE) {
            // 切换到7天天气
//            activityMainBinding.rv24hWeather.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_out_left));
            activityMainBinding.rv24hWeather.setVisibility(View.GONE);
            
            activityMainBinding.rv7dayWeatherList.setVisibility(View.VISIBLE);
//            activityMainBinding.rv7dayWeatherList.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_in_right));
            
            // 移动指示器
//            activityMainBinding.im24hWeatherIndicator.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_out_left));
            activityMainBinding.im24hWeatherIndicator.setVisibility(View.GONE);
            activityMainBinding.im7dayWeatherIndicator.setVisibility(View.VISIBLE);
            activityMainBinding.im7dayWeatherIndicator.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_in_right));
            
            // 更新文字颜色
            activityMainBinding.tv24hWeather.setTextColor(getResources().getColor(R.color.weather_text_color_70));
            activityMainBinding.tv7dayWeather.setTextColor(getResources().getColor(R.color.weather_text_color));
        } else {
            // 切换到24小时天气
//            activityMainBinding.rv7dayWeatherList.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_out_left));
            activityMainBinding.rv7dayWeatherList.setVisibility(View.GONE);
            
            activityMainBinding.rv24hWeather.setVisibility(View.VISIBLE);
//            activityMainBinding.rv24hWeather.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_in_right));
            
            // 移动指示器
//            activityMainBinding.im7dayWeatherIndicator.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_out_left));
            activityMainBinding.im7dayWeatherIndicator.setVisibility(View.GONE);
            activityMainBinding.im24hWeatherIndicator.setVisibility(View.VISIBLE);
            activityMainBinding.im24hWeatherIndicator.startAnimation(AnimationUtils.loadAnimation(this, R.anim.slide_in_left));
            
            // 更新文字颜色
            activityMainBinding.tv7dayWeather.setTextColor(getResources().getColor(R.color.weather_text_color_70));
            activityMainBinding.tv24hWeather.setTextColor(getResources().getColor(R.color.weather_text_color));
        }
    }

    private void show24DayWeather(int visible) {
        activityMainBinding.rv24hWeather.setVisibility(visible);
        activityMainBinding.im24hWeatherIndicator.setVisibility(visible);
        if (visible == View.VISIBLE) {
            activityMainBinding.tv24hWeather.setTextColor(getColor(R.color.weather_text_color));
        } else {
            activityMainBinding.tv24hWeather.setTextColor(getColor(R.color.weather_text_color_60));
        }
    }


}