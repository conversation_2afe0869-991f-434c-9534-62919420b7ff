package com.sgmw.lingos.weather.utils;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.util.Log;

import java.lang.ref.WeakReference;

import timber.log.Timber;

/**
 * <AUTHOR> guo shu dong
 * @date : 2023-1-17 17:02
 * @desc :
 */
public class CommonUtils {
    private static final String logTag = LOG_TAG + "-CommonUtils:";
    private WeakReference<Activity> topActivityRef = null;
    private static volatile CommonUtils instance = null;

    private CommonUtils() {
    }

    public static CommonUtils getInstance() {
        if (instance == null) {
            synchronized (CommonUtils.class) {
                if (instance == null) {
                    instance = new CommonUtils();
                }
            }
        }
        return instance;
    }

    /**
     * 设置当前顶层 Activity
     * @param activity 当前 Activity
     */
    public void setTopActivity(Activity activity) {
        if (activity == null) {
            topActivityRef = null;
            return;
        }
        topActivityRef = new WeakReference<>(activity);
    }

    /**
     * 获取当前顶层 Activity
     * @return 当前顶层 Activity，如果已被回收则返回 null
     */
    public Activity getTopActivity() {
        return topActivityRef != null ? topActivityRef.get() : null;
    }

    /**
     * 判断应用是否在前台
     * @return true: 在前台, false: 不在前台
     */
    public boolean isTopPackage() {
        Activity activity = getTopActivity();
        return activity != null && !activity.isFinishing() && !activity.isDestroyed();
    }

    /**
     * 获取应用内存大小
     */
    public static double getRunningMemory(Context context) {
        double mem = 0.0D;
        try {
            // 统计进程的内存信息 totalPss
            ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
            final Debug.MemoryInfo[] memInfo = activityManager.getProcessMemoryInfo(new int[]{android.os.Process.myPid()});
            if (memInfo.length > 0) {

                /**
                 * 读取内存信息,跟Android Profiler 分析一致
                 */
                String java_mem = memInfo[0].getMemoryStat("summary.java-heap");
                String native_mem = memInfo[0].getMemoryStat("summary.native-heap");
                String graphics_mem = memInfo[0].getMemoryStat("summary.graphics");
                String stack_mem = memInfo[0].getMemoryStat("summary.stack");
                String code_mem = memInfo[0].getMemoryStat("summary.code");
                String others_mem = memInfo[0].getMemoryStat("summary.system");
                String others = memInfo[0].getMemoryStat("summary.private-other");
                //应用总的运行时内存
                String total_pss = memInfo[0].getMemoryStat("summary.total-pss");
                final int dalvikPss = convertToInt(total_pss, 0);
                Log.i(logTag, " getRunningMemory pss mem: java_mem is" + java_mem + "\n" + ": native_mem is " + native_mem + "\n" + ": graphics_mem is " + graphics_mem + "\n" + ": stack_mem is " + stack_mem + "\n" + ": code_mem is " + code_mem + "\n" + ": system_mem is " + others_mem + "\n" + ": private-other is " + others + "\n" + ": total_pss is " + total_pss + "\n");
                if (dalvikPss >= 0) {
                    // Mem in MB
                    mem = dalvikPss / 1024.0D;
                }
            }
        } catch (Exception e) {
            Timber.e("%s:getRunningMemory Exception: %s", logTag, e.getMessage());
        }
        Log.i(logTag, "mem：" + mem);
        return mem;
    }

    /**
     * 转化为int
     *
     * @param value        传入对象
     * @param defaultValue 发生异常时，返回默认值
     * @return
     */
    public final static int convertToInt(Object value, int defaultValue) {

        if (value == null || "".equals(value.toString().trim())) {
            return defaultValue;
        }

        try {
            return Integer.parseInt(value.toString());
        } catch (Exception e) {

            try {
                return Integer.parseInt(String.valueOf(value));
            } catch (Exception e1) {

                try {
                    return Double.valueOf(value.toString()).intValue();
                } catch (Exception e2) {
                    return defaultValue;
                }
            }
        }
    }

}

