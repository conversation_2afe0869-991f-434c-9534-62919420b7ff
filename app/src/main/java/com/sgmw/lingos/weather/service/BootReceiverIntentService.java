package com.sgmw.lingos.weather.service;

import android.app.IntentService;
import android.content.Intent;
import android.os.Handler;

import androidx.annotation.Nullable;

import com.sgmw.lingos.weather.WeatherApp;
import com.sgmw.lingos.weather.manager.WeatherManger;
import com.sgmw.lingos.weather.utils.LogUtils;

/**
 * Description:
 * Author: shaofengf
 * Create: 2024/7/8 16:05
 */

public class BootReceiverIntentService extends IntentService {
    private String TAG = BootReceiverIntentService.class.getSimpleName();

    public BootReceiverIntentService() {
        super("BootReceiverIntentService");
    }

    @Override
    protected void onHandleIntent(@Nullable Intent intent) {
        LogUtils.i(TAG,"onHandleIntent");
        new Handler().postDelayed(() -> {
            WeatherManger.getInstance(WeatherApp.getInstance());
        },2000);
    }
}
