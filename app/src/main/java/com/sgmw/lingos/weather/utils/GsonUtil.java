// Decompiled by Jad v1.5.8g. Copyright 2001 <PERSON>.
// Jad home page: http://www.kpdus.com/jad.html
// Decompiler options: packimports(3)
// Source File Name:   GsonUtil.java

package com.sgmw.lingos.weather.utils;

import android.util.Log;

import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * GSON解析类
 */
public class GsonUtil {
    private static final String TAG = "GsonUtil";

    public static Gson GSON;

    static {
        GSON = new GsonBuilder()
                .serializeSpecialFloatingPointValues()
                .create();
    }

    /**
     * 对象转json字符串
     */
    public static String toJson(Object obj) {
        try {
            return GSON.toJson(obj);
        } catch (Exception e) {
            Log.e(TAG, "toJson fail:" + e.getMessage());
        }
        return "";
    }

    @Nullable
    public static HashMap toMap(String jsonString) {
        if (jsonString == null) {
            return null;
        }
        try {
            return GSON.fromJson(jsonString, HashMap.class);
        } catch (Exception e) {
            Log.e(TAG, "toMap fail:" + e.getMessage());
        }
        return null;
    }

    /**
     * json转对象
     */
    @Nullable
    public static <T> T fromJson(String json, Class<T> cla) {
        try {
            return GSON.fromJson(json, cla);
        } catch (Exception e) {
            Log.e(TAG, "toMap fail:" + e.getMessage());
        }
        return null;
    }

    /**
     * json转对象
     */
    @Nullable
    public static <T> T fromJson(String json, Type type) {
        try {
            return GSON.fromJson(json, type);
        } catch (Exception e) {
            Log.e(TAG, "fromJson fail:" + e.getMessage());
        }
        return null;
    }

    /**
     * json转数组
     */
    public static <T> List<T> getBeans(String jsonString, Type type) {
        List<T> list = new ArrayList<T>();
        try {
            list = GSON.fromJson(jsonString, type);
        } catch (Exception e) {
            Log.e(TAG, "getBeans fail:" + e.getMessage());
        }
        return list;
    }

    /**
     * json转数组
     *
     * @param jsonString
     * @param cls
     * @return
     */
    public static <T> List<T> getBeansList(String jsonString, Class<T> cls) {
        List<T> list = new ArrayList<T>();
        try {
            JsonArray arry = JsonParser.parseString(jsonString).getAsJsonArray();
            for (JsonElement jsonElement : arry) {
                list.add(GSON.fromJson(jsonElement, cls));
            }
        } catch (Exception e) {
        }
        return list;
    }


    /**
     * 获取json字符串对应key的value值
     */
    @Nullable
    public static String getValueByKey(String key, String jsonString) {
        try {
            JsonObject jsonObject = JsonParser.parseString(jsonString).getAsJsonObject();
            return jsonObject.get(key).toString();
        } catch (Exception e) {
            Log.e(TAG, "getValueByKey fail:" + e.getMessage());
        }
        return null;
    }
}
