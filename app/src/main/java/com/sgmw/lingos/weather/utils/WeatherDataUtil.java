package com.sgmw.lingos.weather.utils;

import java.util.HashMap;

public class WeatherDataUtil {
   final static HashMap<String,String> mWindMap = new HashMap<String,String>();

    public static void initData() {
        mWindMap.put("N","北风");
        mWindMap.put("NNE","东北偏北风");
        mWindMap.put("NE","东北风");
        mWindMap.put("ENE","东北偏东风");
        mWindMap.put("E","东风");
        mWindMap.put("ESE","东南偏东风");
        mWindMap.put("SE","东南风");
        mWindMap.put("SSE","东南偏南风");
        mWindMap.put("S","南风");
        mWindMap.put("SSW","西南偏南风");
        mWindMap.put("SW","西南偏西风");
        mWindMap.put("WSW","东南偏东风");
        mWindMap.put("W","西风");
        mWindMap.put("WNW","西北偏西风");
        mWindMap.put("NW","西北风");
        mWindMap.put("NNW","西北偏北风");
    }
    public static String getWindName(String windDir){
        return mWindMap.get(windDir);
    }
    public static String getWindType(String windSpeed){
        double doubleValue = Double.parseDouble(windSpeed);
        if (doubleValue<1) {
            return "0级";
        }else if (doubleValue>=1&&doubleValue<=5){
            return "1级";
        }else if (doubleValue>5&&doubleValue<=11){
            return "2级";
        }else if (doubleValue>11&&doubleValue<=19){
            return "3级";
        }else if (doubleValue>19&&doubleValue<=28){
            return "4级";
        }else if (doubleValue >28) {
            return "05级";
        }
        return "0级";
    }
}
