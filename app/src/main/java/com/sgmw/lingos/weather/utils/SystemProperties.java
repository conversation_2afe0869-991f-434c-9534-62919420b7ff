package com.sgmw.lingos.weather.utils;

import android.util.Log;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class SystemProperties {
    public static String get(String key) {
        String value = "";
        try {
            Class sp = Class.forName("android.os.SystemProperties");
            Method getMethod = sp.getMethod("get", String.class);
            value = (String) getMethod.invoke(null, key);
            Log.i("SystemProperties "," SystemProperties get {"+key+"} value  {"+value+"}");
        } catch (ClassNotFoundException | NoSuchMethodException|
                IllegalAccessException | InvocationTargetException e) {
            Log.e("SP", "" + e);
        }
        return value;
    }

    public static void set(String key, String value) {
        try {
            Class sp = Class.forName("android.os.SystemProperties");
            Method getMethod = sp.getMethod("set", String.class, String.class);
            getMethod.invoke(null, key, value);
            Log.i("SystemProperties","set key ->"+key +"; value : "+value);
        } catch (ClassNotFoundException | NoSuchMethodException|
                IllegalAccessException | InvocationTargetException e) {
            Log.e("SP", "" + e);
        }
    }

    public static String get(String key, String def) {
        String value = "";
        try {
            Class sp = Class.forName("android.os.SystemProperties");
            Method getMethod = sp.getMethod("get", String.class, String.class);
            value = (String) getMethod.invoke(null, key, def);
        } catch (ClassNotFoundException | NoSuchMethodException|
                IllegalAccessException | InvocationTargetException e) {
            Log.e("SP", "" + e);
        }
        return value;
    }
}
