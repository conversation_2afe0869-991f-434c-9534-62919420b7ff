package com.sgmw.lingos.weather.adapter;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sgmw.entity.Forecast;
import com.sgmw.lingos.weather.R;
import com.sgmw.lingos.weather.utils.Constants;

import org.jetbrains.annotations.NotNull;

import java.util.List;

public class WeekAdapter extends RecyclerView.Adapter<WeekAdapter.ViewHolder> {
    public static final String TAG = "WeekAdapter";
    List<Forecast> hourlyDataItemList;

    public WeekAdapter(List<Forecast> hourlyDataItems) {
        this.hourlyDataItemList = hourlyDataItems;
    }

    class ViewHolder extends RecyclerView.ViewHolder {


        private final TextView weatherTemp;
        private final TextView weatherDesc;
        private final ImageView weatherIcon;
        private final TextView currentDayTime;
        private final TextView weatherDayTime;

        public ViewHolder(@NonNull @NotNull View itemView) {
            super(itemView);
            currentDayTime = itemView.findViewById(R.id.item_week_time_now);
            weatherDayTime = itemView.findViewById(R.id.item_week_day_time);
            weatherIcon = itemView.findViewById(R.id.item_week_day_icon);
            weatherDesc = itemView.findViewById(R.id.item_week_day_desc);
            weatherTemp = itemView.findViewById(R.id.item_week_day_temp);
        }

        public void bindHourlyDataItem(Forecast dataItem, int position) {
            if (position == 0) {
                currentDayTime.setVisibility(View.VISIBLE);
                weatherDayTime.setVisibility(View.INVISIBLE);
            } else {
                currentDayTime.setVisibility(View.INVISIBLE);
                weatherDayTime.setVisibility(View.VISIBLE);
            }
            weatherTemp.setText(dataItem.getTempNight() + "/" + dataItem.getTempDay() + "°C");
            String predictDate = dataItem.getPredictDate();
            String timeString = "";
            String[] split = predictDate.split("-");
            if (split != null) {
                if (split.length == 3) {
                    timeString = split[1] + "/" + split[2];
                } else if (split.length == 2) {
                    timeString = split[0] + "/" + position;
                }
            }

            weatherDayTime.setText(timeString);
            weatherDesc.setText(dataItem.getConditionDay());
            Integer resId = Constants.WEATHER_NAME_DES_ICON_DAY.get(dataItem.getConditionDay());
            weatherIcon.setImageResource(resId != null ? resId : R.mipmap.ic_sunny);
            Log.i(TAG, "57 bindHourlyDataItem: " + dataItem.toString());
        }
    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.hourly_week_item_layout, parent, false);
        ViewHolder viewHolder = new ViewHolder(view);
        return viewHolder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

        Forecast hourlyDataItemH = hourlyDataItemList.get(position);

        holder.bindHourlyDataItem(hourlyDataItemH, position);

    }


    @Override
    public int getItemCount() {
        return hourlyDataItemList.size();
    }
}
