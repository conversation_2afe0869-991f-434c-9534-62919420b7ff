package com.sgmw.lingos.weather.manager;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import com.sgmw.lingos.weather.utils.LogUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 广播管理器
 * 统一管理跨应用广播，避免 Android 8.0+ 的限制
 */
public class BroadcastManager {
    private static final String TAG = "BroadcastManager";
    
    // 广播 Action
    public static final String ACTION_WEATHER_REFRESH = "com.sgmw.weather.refresh_weather";
    public static final String ACTION_WEATHER_STATUS = "com.sgmw.weather.change_status";

    // 目标应用包名列表
    private static final Set<String> TARGET_PACKAGES = new HashSet<>();
    
    static {
        TARGET_PACKAGES.add("com.sgmw.launcher");            // 天气卡片应用
        TARGET_PACKAGES.add("com.sgmw.navi");      // 天气库导航应用
        TARGET_PACKAGES.add("com.android.systemui");      // 待机界面
        TARGET_PACKAGES.add("com.sgwm.system.scenemode");      // 场景模式
    }
    
    private static BroadcastManager instance;
    private final Context mContext;
    
    private BroadcastManager(Context context) {
        this.mContext = context.getApplicationContext();
    }
    
    public static BroadcastManager getInstance(Context context) {
        if (instance == null) {
            synchronized (BroadcastManager.class) {
                if (instance == null) {
                    instance = new BroadcastManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 发送天气刷新广播
     */
    public void sendWeatherRefreshBroadcast(Bundle data) {
        sendBroadcast(ACTION_WEATHER_REFRESH, data);
    }
    
    /**
     * 发送天气状态变化广播
     */
    public void sendWeatherStatusBroadcast(Bundle data) {
        sendBroadcast(ACTION_WEATHER_STATUS, data);
    }
    

    /**
     * 发送广播到所有目标应用
     */
    private void sendBroadcast(String action, Bundle data) {
        try {
            for (String packageName : TARGET_PACKAGES) {
                Intent intent = new Intent(action);
                if (data != null) {
                    intent.putExtras(data);
                }
                
                // 设置为显式广播，避免 Android 8.0+ 的限制
                intent.setPackage(packageName);
                
                // 发送广播
                mContext.sendBroadcast(intent);
                LogUtils.d(TAG, "sendBroadcast: 已发送 " + action + " 到 " + packageName);
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "sendBroadcast: 发送广播失败"+ e);
        }
    }
    
    /**
     * 添加目标应用包名
     */
    public void addTargetPackage(String packageName) {
        synchronized (TARGET_PACKAGES){
            if (!TARGET_PACKAGES.contains(packageName)) {
                TARGET_PACKAGES.add(packageName);
                LogUtils.i(TAG, "addTargetPackage: 添加目标应用 " + packageName);
            }
        }

    }
    
    /**
     * 移除目标应用包名
     */
    public void removeTargetPackage(String packageName) {
        if (TARGET_PACKAGES.remove(packageName)) {
            LogUtils.i(TAG, "removeTargetPackage: 移除目标应用 " + packageName);
        }
    }
    
    /**
     * 获取目标应用列表
     */
    public List<String> getTargetPackages() {
        return new ArrayList<>(TARGET_PACKAGES);
    }
    
    /**
     * 检查应用是否已安装
     */
    public boolean isPackageInstalled(String packageName) {
        try {
            mContext.getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 发送广播到已安装的应用
     */
    public void sendBroadcastToInstalledApps(String action, Bundle data) {
        try {
            synchronized (TARGET_PACKAGES){
                for (String packageName : TARGET_PACKAGES) {
                    if (isPackageInstalled(packageName)) {
                        Intent intent = new Intent(action);
                        if (data != null) {
                            intent.putExtras(data);
                        }
                        intent.setPackage(packageName);
                        mContext.sendBroadcast(intent);
                        LogUtils.d(TAG, "sendBroadcastToInstalledApps: 已发送到 " + packageName);
                    } else {
                        LogUtils.w(TAG, "sendBroadcastToInstalledApps: 应用未安装 " + packageName);
                    }
                }
            }

        } catch (Exception e) {
            LogUtils.e(TAG, "sendBroadcastToInstalledApps: 发送广播失败"+e);
        }
    }
} 