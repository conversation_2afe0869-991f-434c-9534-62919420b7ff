package com.sgmw.lingos.weather.helper;

import android.content.Context;
import android.content.SharedPreferences;

public class PrefUtils {
    // 首文件工具类
    private SharedPreferences sp;
    private static volatile PrefUtils instance;
    private Context context;
    private String spName = "";
    private PrefUtils() {
    }
    public static PrefUtils getInstance(){
        if(instance == null){
            synchronized (PrefUtils.class){
                if(instance == null){
                    instance = new PrefUtils();
                }
            }
        }
        return instance;
    }

    /**
     * 必须初始化才能使用的方法
     * @param context 上下文，获取sharedPrefrence
     * @param spName sp的表名
     */
    public void initSpUtils(Context context, String spName){
        this.context = context;
        sp = context.getSharedPreferences(spName, Context.MODE_PRIVATE);
    }

    //boolean
    public void setBoolean(String key, Boolean value) {
        sp.edit().putBoolean(key, value).apply();
    }
    public boolean getBoolean(String key) {
        return sp.getBoolean(key, false);
    }
    public boolean getBoolean(String key, boolean defValue) {
        return sp.getBoolean(key, defValue);
    }

    //float
    public void setFloat(String key, float value) {
        sp.edit().putFloat(key, value).apply();
    }
    public float getFloat(String key) {
        return sp.getFloat(key, 0f);
    }
    public float getFloat(String key, float defValue) {
        return sp.getFloat(key, defValue);
    }

    //int
    public void setInt(String key, int value) {
        sp.edit().putInt(key, value).apply();
    }
    public int getInt(String key) {
        return sp.getInt(key, 0);
    }
    public int getInt(String key, int defValue) {
        return sp.getInt(key, defValue);
    }

    //String
    public void setString(String key, String value) {
        sp.edit().putString(key, value).apply();
    }
    public String getString(String key) {
        if(sp != null){
            return sp.getString(key, "");
        } else {
            return "";
        }

    }
    public String getString(String key, String defValue) {
        return sp.getString(key, defValue);
    }



}
