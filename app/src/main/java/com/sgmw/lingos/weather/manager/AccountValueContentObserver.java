package com.sgmw.lingos.weather.manager;

import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.provider.Settings;

import com.sgmw.lingos.weather.utils.Constants;
import com.sgmw.lingos.weather.utils.LogUtils;

public class AccountValueContentObserver extends ContentObserver {

    private final static String TAG = AccountValueContentObserver.class.getSimpleName();
    private static AccountValueContentObserver mInstance;
    private Context mContext;
    private IAccountInfoListener mIAccountInfoListener;

    /**
     * Creates a content observer.
     *
     * @param handler The handler to run {@link #onChange} on, or null if none.
     */
    public AccountValueContentObserver(Context context, Handler handler, IAccountInfoListener iAccountInfoListener) {
        super(handler);
        mContext = context;
        mIAccountInfoListener = iAccountInfoListener;
        initData();
    }

    public static AccountValueContentObserver getInstance(Context context, Handler handler, IAccountInfoListener iAccountInfoListener) {
        if (null == mInstance) {
            synchronized (AccountValueContentObserver.class) {
                mInstance = new AccountValueContentObserver(context, handler, iAccountInfoListener);
            }
        }
        return mInstance;
    }


    public void initData() {
        Uri uriLogin = Settings.Global.getUriFor(Constants.SETTINGS_KEY_LOGIN);
        mContext.getContentResolver().registerContentObserver(uriLogin, true, this);
    }

    @Override
    public void onChange(boolean selfChange, Uri uri) {
        LogUtils.i(TAG, "the sms table has changed:" + uri);
        if (Settings.Global.getUriFor(Constants.SETTINGS_KEY_LOGIN).equals(uri)) {
            getLoginInfoChange();
        } else {
            LogUtils.i(TAG, "onChange No Uri");
        }
    }

    public void getLoginInfoChange() {
        String userInfo = Settings.Global.getString(mContext.getContentResolver(), Constants.SETTINGS_KEY_LOGIN);
        LogUtils.i(TAG, "userInfo:"+userInfo);
        if (mIAccountInfoListener != null) {
            mIAccountInfoListener.onUserInfoChange(userInfo);
        }
    }

    public interface IAccountInfoListener {
        void onUserInfoChange(String userInfo);
    }
}
