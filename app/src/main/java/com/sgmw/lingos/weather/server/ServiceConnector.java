package com.sgmw.lingos.weather.server;

import static com.sgmw.lingos.weather.utils.Constants.LOG_TAG;
import static com.sgmw.lingos.weather.utils.Constants.SP_KEY_LAST_UPDATE_TIME;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import androidx.lifecycle.MutableLiveData;


import com.sgmw.entity.LiveIndex;

import com.sgmw.entity.LiveIndexExt;
import com.sgmw.entity.WeatherData;
import com.sgmw.lingos.weather.callback.WeatherNotifyCallBack;
import com.sgmw.lingos.weather.helper.PrefUtils;
import com.sgmw.lingos.weather.utils.LogUtils;

import java.util.List;

import timber.log.Timber;

public class ServiceConnector implements WeatherNotifyCallBack {
    private String TAG = "ServiceConnector";
    private static final String logTag = LOG_TAG + "-ServiceConnector:";
    MutableLiveData<WeatherData> weatherData = new MutableLiveData<>();
    MutableLiveData<String> currentCity = new MutableLiveData<>();
    MutableLiveData<LiveIndexExt> liveIndexExt = new MutableLiveData<>();
    MutableLiveData<Boolean> isLoadingSuccess = new MutableLiveData<>();
    MutableLiveData<Boolean> isNight = new MutableLiveData<>();
    public MutableLiveData<String> updateTimeLiveData = new MutableLiveData<>();//获取时间显示格式 12小时制 24小时制

    public MutableLiveData<String> getCurrentCity() {
        return currentCity;
    }

    public MutableLiveData<WeatherData> getWeatherData() {
        return weatherData;
    }

    public MutableLiveData<String> getUpdateTimeLiveData() {
        return updateTimeLiveData;
    }

    public MutableLiveData<LiveIndexExt> getLiveIndexExt() {
        return liveIndexExt;
    }

    public MutableLiveData<Boolean> getIsLoadingSuccess() {
        return isLoadingSuccess;
    }

    public MutableLiveData<Boolean> getIsNight() {
        return isNight;
    }

    private static volatile ServiceConnector instance;

    private ServiceConnector() {
    }

    public static ServiceConnector getInstance() {
        if (instance == null) {
            synchronized (ServiceConnector.class) {
                if (instance == null) {
                    instance = new ServiceConnector();
                }
            }
        }
        return instance;
    }


    // 创建解析天气指数（天气返回不一定每个指数都用）
    public LiveIndexExt createLiveIndexExt(List<LiveIndex> list) {
        LiveIndexExt liveIndexExt = new LiveIndexExt();
        if (list == null || list.size() == 0) {
            return liveIndexExt;
        }


        for (LiveIndex item : list) {
            switch (item.getName()) {
                case "洗车指数":
                    liveIndexExt.setCostWash(item.getStatus());
                    break;
                case "交通指数":
                    liveIndexExt.setCostTraffic(item.getStatus());
                    break;
                case "旅游指数":
                    liveIndexExt.setCostTourism(item.getStatus());
                    break;
                case "感冒指数":
                    liveIndexExt.setCostCold(item.getStatus());
                    break;
                case "空气污染扩散指数":
                    liveIndexExt.setAirPolution(item.getStatus());
                    break;
                case "紫外线指数":
                    liveIndexExt.setCostUltraviolet(item.getStatus());
                    break;
                case "穿衣指数":
                    liveIndexExt.setCostDress(item.getStatus());
                    break;
                case "运动指数":
                    liveIndexExt.setCostSport(item.getStatus());
                    break;
                default:
                    Timber.i("%s liveIndexExt = %s", logTag, item.getName());
            }
        }

        return liveIndexExt;

    }


    @Override
    public void notifyChangeWeatherInfo(Context context, WeatherData weatherdata) {
        Timber.i("%s notifyChangeWeatherInfo:%s", logTag, weatherdata);
        if (weatherdata != null) {
//            String name = (TextUtils.isEmpty(weatherdata.getCityNameStr()) ? "" : weatherdata.getCityNameStr()) + "-" + weatherdata.getDistrictName();
//            currentCity.postValue(name);
            liveIndexExt.postValue(createLiveIndexExt(weatherdata.getLiveIndex()));
            weatherData.postValue(weatherdata);
            String updateTime = PrefUtils.getInstance().getString(SP_KEY_LAST_UPDATE_TIME);
            //实际上updateTime 不会为空，只要请求了数据走到这里之前已经把数据缓存起来了
            if (!TextUtils.isEmpty(updateTime)) {
                LogUtils.i(TAG,"更新时间： "+updateTime);
                updateTimeLiveData.postValue(updateTime);
            } else {
                LogUtils.i(TAG,"异常情况 缓存的 更新时间为空");
            }
            isLoadingSuccess.postValue(true);
        } else {
            isLoadingSuccess.postValue(false);
        }
    }

    @Override
    public void notifyChangeCityInfo(String city) {
        LogUtils.i(TAG,"city : "+city);
        if (!TextUtils.isEmpty(city)) {
            currentCity.postValue(city);
        }
    }

    @Override
    public void notifySetCallbackSuccess() {

    }
}
