package com.sgmw.lingos.weather.utils;
/**
 *
 * 定位的几个基本信息
 */
public class LocateInfo {
    /**
     * 经度
     */
    private double longitude;

    public double getLongitude() {
        return longitude;
    }

    public double getLatitude() {
        return Latitude;
    }

    public boolean isChina() {
        return isChina;
    }

    /**
     * 维度
     */
    private double Latitude;
    /**
     * 是否在中国
     */
    private boolean isChina;

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public void setLatitude(double latitude) {
        Latitude = latitude;
    }

    public void setChina(boolean china) {
        isChina = china;
    }
}
