package com.sgmw.lingos.weather.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.sgmw.lingos.weather.manager.WeatherManger;

import java.util.Calendar;

public class TimeChangeReceiver extends BroadcastReceiver {
    private static final String TAG = "TimeChangeReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        //系统每分钟会发出该广播
        if (intent.getAction().equals(Intent.ACTION_TIME_TICK)) {
            Calendar calendar = Calendar.getInstance();
            int minute = calendar.get(Calendar.MINUTE);
            if (minute == 0) {
                //整点
                WeatherManger.getInstance(context).getNavAreaInfo();
                Log.i(TAG, "20:onReceive:  整点请求天气");
            } else {
                Log.i(TAG, "25:onReceive: 非整点模式 ");
            }

//            WeatherManger.getInstance(context).setThemeMode();
        }
    }
}
