package com.sgmw.lingos.weather.utils;


import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;

import com.sensorsdata.analytics.android.sdk.SAConfigOptions;
import com.sensorsdata.analytics.android.sdk.SensorsAnalyticsAutoTrackEventType;
import com.sensorsdata.analytics.android.sdk.SensorsDataAPI;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 神策埋点管理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/23
 */
public class SensorsDataManager {

    private static final String TAG = SensorsDataManager.class.getSimpleName();

    // debug 模式的数据接收地址 （测试，测试项目）
    final static String SA_SERVER_URL_DEBUG = "http://apigw-test.sgmwcloud.com.cn/api/log/appBuryingPoint?project=zhilian_st_202302&token=e0a40d90-01d8-7fa0-08df-0e72e316f751";

    // release 模式的数据接收地址（发版，正式项目） 暂时使用测试环境
    final static String SA_SERVER_URL_RELEASE = "https://buryingpoint.sgmwcloud.com.cn:8001/api/log/appBuryingPoint?project=zhilian_pro_202302&token=155e89db-4e0f-fa73-8e39-761087103f61";

    private static String CAR_VIN = "";
    private static String CAR_PDSN = "";
    private static String SOFT_ERSION = "";
    //默认设置LV3
    private static String CAR_TYPE = "LV3";
    private static String CAR_SERIES = "";

    /**
     * 初始化 SDK 、设置自动采集、设置公共属性
     */
    public static void initSensorsDataSDK(Context context) {
        try {
            // 设置 SAConfigOptions，传入数据接收地址 SA_SERVER_URL
            SAConfigOptions saConfigOptions = new SAConfigOptions(GlobalENV.initApiMode(context) ? SA_SERVER_URL_DEBUG : SA_SERVER_URL_RELEASE);

            // 通过 SAConfigOptions 设置神策 SDK 自动采集 options
            saConfigOptions.setAutoTrackEventType(SensorsAnalyticsAutoTrackEventType.APP_START | // 自动采集 App 启动事件
                            SensorsAnalyticsAutoTrackEventType.APP_END | // 自动采集 App 退出事件
                            SensorsAnalyticsAutoTrackEventType.APP_VIEW_SCREEN | // 自动采集 App 浏览页面事件
                            SensorsAnalyticsAutoTrackEventType.APP_CLICK)   // 自动采集控件点击事件
                    /*.enableLog(isDebugMode(context))*/
                    .enableLog(true)// 开启神策调试日志，默认关闭(调试时，可开启日志)。默认开启
                    .enableTrackAppCrash(); // 开启 crash 采集

            // 需要在主线程初始化神策 SDK
            SensorsDataAPI.startWithConfigOptions(context, saConfigOptions);
            //登录id
            String userInfo = Settings.Global.getString(context.getContentResolver(), Constants.SETTINGS_KEY_LOGIN);
            LogUtils.i(TAG, "initSensorsDataSDK userInfo:" + userInfo);
            login(userInfo);
            // 初始化 SDK 后，可以获取应用名称设置为公共属性
            JSONObject properties = new JSONObject();
            properties.put("appName", getAppName(context));
            SensorsDataAPI.sharedInstance().registerSuperProperties(properties);
            getSoftVersion();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 登录 神策埋点
     *
     * @param userInfo
     */
    public static void login(String userInfo) {
        LogUtils.i(TAG, "userInfo:" + userInfo);
        if (!TextUtils.isEmpty(userInfo)) {
            try {
                JSONObject jsonObject = new JSONObject(userInfo);
                if (jsonObject.has("userIdStr") && !jsonObject.isNull("userIdStr")) {
                    String userIdStr = jsonObject.getString("userIdStr");
                    LogUtils.i(TAG, "userIdStr:" + userIdStr);
                    if (!TextUtils.isEmpty(userIdStr)) {
                        SensorsDataAPI.sharedInstance().login(userIdStr);
                        return;
                    }
                }
            } catch (JSONException e) {
                LogUtils.e(TAG, e);
            }
        }
        String anonymousId = SensorsDataAPI.sharedInstance().getAnonymousId();
        Log.i(TAG, "anonymousId===》" + anonymousId);
        SensorsDataAPI.sharedInstance().login(anonymousId);
    }

    /**
     * @param context App 的 Context
     *                获取应用程序名称
     */
    public static CharSequence getAppName(Context context) {
        if (context == null) {
            return "";
        }
        try {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager == null) {
                return "";
            }
            ApplicationInfo appInfo = packageManager.getApplicationInfo(context.getPackageName(), PackageManager.GET_META_DATA);
//            return appInfo.loadLabel(packageManager);
            //应用名获取错误，先暂且写死
            return "天气";
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * @param context App 的 Context
     * @return debug return true,release return false
     * 用于判断是 debug 包，还是 relase 包
     */
    public static boolean isDebugMode(Context context) {
        try {
            ApplicationInfo info = context.getApplicationInfo();
            return (info.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    public static void enterWeather(String event_page, String channel) {
        try {
            JSONObject properties = new JSONObject();
            properties.put("class_code", "weather_element_click");
            properties.put("class_name", "天气元素点击");
            properties.put("event_code", "weather_enter");
            properties.put("event_name", "进入天气应用");
            properties.put("event_page", event_page);
            properties.put("channel", channel);
            properties.put("vin", CAR_VIN);
            Log.e(TAG, "121 enterWeather: " + CAR_VIN);
            properties.put("pdsn", CAR_PDSN);
            properties.put("tice_version", SOFT_ERSION);//车机版本号
            properties.put("car_type", CAR_TYPE);
            properties.put("car_series", CAR_SERIES);
            SensorsDataAPI.sharedInstance().track("weather_element_click", properties);
            Log.i(TAG, " enterWeather: ");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 事件跟踪
     */
    public static void useDuring(String durring) {
        if ("0".equals(durring)) {
            Log.i(TAG, " useDuring: 一闪而过");
            return;
        }
        try {
            JSONObject properties = new JSONObject();
            properties.put("class_code", "weather_page_browse");//事件类型英文名
            properties.put("class_name", "车机页面浏览");//事件类型中文名
            properties.put("event_code", "browse_weather_page");//事件英文名
            properties.put("event_name", "浏览天气页面");//事件中文名
            properties.put("event_page", "天气");//事件发生页面
            properties.put("vin", CAR_VIN);//车架号
            properties.put("pdsn", CAR_PDSN);//车机唯一码PDSN
            properties.put("tice_version", SOFT_ERSION);//车机版本号
            properties.put("car_type", CAR_TYPE);
            properties.put("car_series", CAR_SERIES);
            JSONObject newProperties = new JSONObject();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("event_duration", durring);
            newProperties.put("weather", jsonObject);
            //new_properties的valve值必须是JSONArray类型才可上传到神策埋点平台
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(newProperties);
            properties.put("new_properties", newProperties.toString());
            SensorsDataAPI.sharedInstance().track("weather_page_browse", properties);
            Log.i(TAG, " useDuring: " + durring);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void getSoftVersion() {
        String vin = SystemProperties.get("persist.vendor.sgmw.car.info.code");
        String pdsn = SystemProperties.get("persist.vendor.sgmw.ecu.serial.number");
        String softVersion = SystemProperties.get("ro.build.version.customer");
        String firstFour = "";
        if (softVersion == null || TextUtils.isEmpty(softVersion) || softVersion.length() < 4) {
            CAR_SERIES = "ErrorCode";
        } else {
            firstFour = softVersion.substring(0, 4);
        }
        Log.i(TAG, "Vin===" + vin);
        if (!TextUtils.isEmpty(vin)) {
            CAR_VIN = vin;
        }
        if (!TextUtils.isEmpty(pdsn)) {
            CAR_PDSN = pdsn;
        }
        if (!TextUtils.isEmpty(softVersion)) {
            SOFT_ERSION = softVersion;
        }
        if ("TICE".equals(firstFour)) {
            CAR_SERIES = "TICE";
        } else if ("Q200".equals(firstFour)) {
            CAR_SERIES = "F710C";
        }
    }
}
