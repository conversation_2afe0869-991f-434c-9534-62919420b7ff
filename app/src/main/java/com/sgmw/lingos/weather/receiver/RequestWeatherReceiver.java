package com.sgmw.lingos.weather.receiver;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import com.sgmw.lingos.weather.manager.BroadcastManager;
import com.sgmw.lingos.weather.manager.WeatherManger;

public class RequestWeatherReceiver extends BroadcastReceiver {
    private String TAG = "RequestWeatherReceiver";
    private static final String REQUEST_WEATHER = "com.sgmw.weather.request_weather";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (REQUEST_WEATHER.equals(intent.getAction())) {
            Log.i(TAG, "13:onReceive: 请求天气 ");
            if (!TextUtils.isEmpty(intent.getStringExtra("app"))){
                BroadcastManager.getInstance(context).addTargetPackage(intent.getStringExtra("app"));
            }
            WeatherManger.getInstance(context).getNavAreaInfo();
        }
        Log.i(TAG, "16:onReceive:  ");
    }
}
