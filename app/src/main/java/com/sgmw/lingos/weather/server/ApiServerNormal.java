package com.sgmw.lingos.weather.server;



import com.sgmw.entity.ResponseWeatherBean;

import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Headers;
import retrofit2.http.POST;

public interface ApiServerNormal {
    @POST("getWeatherByDistrictId")
    Call<ResponseWeatherBean> getWeatherById(@Body RequestBody body);

    @POST("getWeatherByPosition")
    Call<ResponseWeatherBean> getWeatherByPosition(@Body RequestBody body);

}
