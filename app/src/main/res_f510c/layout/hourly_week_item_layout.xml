<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/item_week_time_now"
        style="@style/common_text_style_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:layout_marginTop="-6dp"
        android:clipToPadding="false"
        android:gravity="center"
        android:text="今天"
        android:textSize="@dimen/s24"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/item_week_day_time"
        style="@style/hour_weather_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="11/28"
        android:textSize="@dimen/s26"
        android:gravity="center"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/item_week_day_icon"
        android:layout_marginTop="@dimen/d16"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/ic_fog"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/item_week_day_time" />

    <TextView
        android:id="@+id/item_week_day_desc"
        style="@style/hour_weather_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="多云"
        android:layout_marginTop="@dimen/d8"
        android:textSize="20sp"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/item_week_day_icon"/>

    <TextView
        android:layout_marginTop="@dimen/d14"
        android:id="@+id/item_week_day_temp"
        style="@style/weather_regular_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="19°C/39°C"
        android:textSize="24sp"
        android:contentDescription="(@hide_uc(value=[true]))"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/item_week_day_desc"/>
</androidx.constraintlayout.widget.ConstraintLayout>