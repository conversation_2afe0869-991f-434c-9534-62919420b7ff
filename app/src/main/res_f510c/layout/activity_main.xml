<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.sgmw.lingos.weather.model.WeatherViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/theme">

        <!--无网络-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_no_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="88dp"
            android:orientation="vertical"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints"
            tools:visibility="gone">

            <ImageView
                android:id="@+id/iv_no_data"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d288"
                android:src="@mipmap/ic_nodata_400"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                style="@style/common_text_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d591"
                android:text="@string/def_net_err"
                android:textSize="@dimen/s32"
                android:contentDescription="(@hide_uc(value=[true]))"
                app:layout_constraintEnd_toEndOf="@id/iv_no_data"
                app:layout_constraintStart_toStartOf="@id/iv_no_data"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_no_data"
                android:layout_width="@dimen/d176"
                android:layout_height="@dimen/d64"
                android:background="@mipmap/ic_reload"
                android:clickable="true"
                android:text="@string/def_reload"
                android:textColor="@color/reload_text_color"
                android:textSize="28sp"
                android:gravity="center"
                android:includeFontPadding="false"
                android:fontFamily="serif"
                android:textFontWeight="600"
                app:layout_constraintEnd_toEndOf="@id/iv_no_data"
                app:layout_constraintStart_toStartOf="@id/iv_no_data"
                app:layout_constraintTop_toBottomOf="@id/iv_no_data" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--加载中-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_loading_data"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="88dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/iv_loading_data"
                android:layout_width="@dimen/d156"
                android:layout_height="@dimen/d156"
                android:layout_marginTop="@dimen/d406"
                android:src="@drawable/weather_animation_loading"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_loading_data"
                style="@style/common_text_style"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d24"
                android:gravity="center"
                android:text="@string/def_loading"
                android:textSize="28sp"
                android:contentDescription="(@hide_uc(value=[true]))"
                app:layout_constraintLeft_toLeftOf="@+id/iv_loading_data"
                app:layout_constraintRight_toRightOf="@+id/iv_loading_data"
                app:layout_constraintTop_toBottomOf="@id/iv_loading_data" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--天气-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_page_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@mipmap/img_sunny"
            android:visibility="gone">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/con_weather"
                android:layout_width="wrap_content"
                android:layout_height="370dp"
                android:layout_marginStart="@dimen/d180"
                android:layout_marginTop="@dimen/d128"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_location"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_location"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_location"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="8dp"
                    android:text="@string/def_city_name"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_location"
                    app:layout_constraintLeft_toRightOf="@+id/iv_location"
                    app:layout_constraintTop_toTopOf="@+id/iv_location" />

                <TextView
                    android:id="@+id/tv_temp_show"
                    style="@style/hour_weather_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:includeFontPadding="true"
                    android:text="@string/def_temperature"
                    android:textSize="120sp"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toTopOf="@+id/cl_air_quality"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_location" />

                <TextView
                    android:id="@+id/tv_unit_du"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="60dp"
                    android:layout_marginTop="26dp"
                    android:gravity="top"
                    android:text="°C"
                    android:textSize="@dimen/s48"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintLeft_toRightOf="@+id/tv_temp_show"
                    app:layout_constraintTop_toTopOf="@+id/tv_temp_show" />


                <TextView
                    android:id="@+id/tv_weather"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d34"
                    android:layout_marginTop="89dp"
                    android:text="@string/def_weather"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintLeft_toRightOf="@+id/tv_unit_du"
                    app:layout_constraintTop_toBottomOf="@+id/iv_location" />

                <ImageView
                    android:id="@+id/iv_weather"
                    android:layout_width="@dimen/d68"
                    android:layout_height="@dimen/d68"
                    android:layout_marginStart="@dimen/d20"
                    android:layout_marginTop="75dp"
                    android:src="@mipmap/ic_sunny"
                    app:layout_constraintStart_toEndOf="@id/tv_weather"
                    app:layout_constraintTop_toBottomOf="@+id/iv_location" />

                <TextView
                    android:id="@+id/tv_maxtemp"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/d13"
                    android:text="@string/def_mintemp"
                    android:textSize="@dimen/s24"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="@+id/tv_weather"
                    app:layout_constraintTop_toBottomOf="@+id/iv_weather"

                    />

                <TextView
                    android:id="@+id/tv_maxtemp_unit"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="°C"
                    android:textSize="@dimen/s24"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_maxtemp"
                    app:layout_constraintStart_toEndOf="@+id/tv_maxtemp"
                    app:layout_constraintTop_toTopOf="@+id/tv_maxtemp" />

                <TextView
                    android:id="@+id/tv_temp_split"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/def_temp_split"
                    android:textSize="@dimen/s24"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_maxtemp"
                    app:layout_constraintStart_toEndOf="@+id/tv_maxtemp_unit"
                    app:layout_constraintTop_toTopOf="@+id/tv_maxtemp" />

                <TextView
                    android:id="@+id/tv_mintemp"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/def_maxtemp"
                    android:textSize="@dimen/s24"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_maxtemp"
                    app:layout_constraintStart_toEndOf="@+id/tv_temp_split"
                    app:layout_constraintTop_toTopOf="@+id/tv_maxtemp" />

                <TextView
                    android:id="@+id/tv_mintemp_unit"
                    style="@style/weather_regular_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="°C"
                    android:textSize="@dimen/s24"
                    android:textStyle="bold"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_maxtemp"
                    app:layout_constraintStart_toEndOf="@+id/tv_mintemp"
                    app:layout_constraintTop_toTopOf="@+id/tv_maxtemp" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_air_quality"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/weather_quality_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent">

                    <ImageView
                        android:id="@+id/iv_air_quality"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/air_you_bg"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_air_quality"
                        style="@style/common_text_style"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/d72dp"
                        android:paddingRight="@dimen/d73dp"
                        android:text="@string/def_air_quality"
                        android:textSize="24sp"
                        android:contentDescription="(@hide_uc(value=[true]))"
                        app:layout_constraintBottom_toBottomOf="@+id/iv_air_quality"
                        app:layout_constraintLeft_toRightOf="@+id/iv_air_quality"
                        app:layout_constraintTop_toTopOf="@+id/iv_air_quality" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_evaluation"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d20"
                    android:text="@string/def_evaluation"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_air_quality"
                    app:layout_constraintLeft_toRightOf="@+id/cl_air_quality"
                    app:layout_constraintTop_toTopOf="@+id/cl_air_quality" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_refresh_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d452"
                android:layout_marginRight="95dp"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">


                <ImageView
                    android:id="@+id/iv_refresh"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@mipmap/ic_refresh"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_update_tip"
                    style="@style/common_text_style"
                    android:paddingBottom="@dimen/d4"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/d52"
                    android:layout_marginStart="@dimen/d12"
                    android:gravity="center"
                    android:text="@string/def_update_time"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_refresh"
                    app:layout_constraintStart_toEndOf="@+id/iv_refresh"
                    app:layout_constraintTop_toTopOf="@+id/iv_refresh" />

                <TextView
                    android:id="@+id/tv_update_time"
                    style="@style/common_text_style"
                    android:paddingBottom="@dimen/d4"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/d52"
                    android:layout_marginStart="@dimen/d21"
                    android:gravity="center"
                    android:text="@string/def_update_clock"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_update_tip"
                    app:layout_constraintStart_toEndOf="@+id/tv_update_tip"
                    app:layout_constraintTop_toTopOf="@+id/tv_update_tip" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!-- 刷新的动画 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_refresh_animal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/d452"
                android:layout_marginRight="214dp"
                android:visibility="gone"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_refresh_animal"
                    android:layout_width="@dimen/d48"
                    android:layout_height="@dimen/d48"
                    android:src="@mipmap/ic_loading"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_update_animal"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d8"
                    android:text="@string/def_update_time_animal"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="@+id/iv_refresh_animal"
                    app:layout_constraintStart_toEndOf="@+id/iv_refresh_animal"
                    app:layout_constraintTop_toTopOf="@+id/iv_refresh_animal" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--24小时天气-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_24h_weather"
                android:layout_width="@dimen/d972"
                android:layout_height="@dimen/d352"
                android:layout_marginStart="@dimen/d140"
                android:layout_marginTop="560dp"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_24h_weather"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d45"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_24h_weather"
                    android:textSize="24sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/im_24h_weather_indicator"
                    android:layout_width="64dp"
                    android:layout_height="5dp"
                    android:layout_marginTop="8dp"
                    android:background="@mipmap/weather_indicator"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_24h_weather"
                    app:layout_constraintRight_toRightOf="@+id/tv_24h_weather"
                    app:layout_constraintTop_toBottomOf="@+id/tv_24h_weather"/>


                <TextView
                    android:id="@+id/tv_7day_weather"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/d46"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_7day_weather"
                    android:textSize="24sp"
                    app:layout_constraintLeft_toRightOf="@+id/tv_24h_weather"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/im_7day_weather_indicator"
                    android:layout_width="64dp"
                    android:layout_height="5dp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"
                    android:background="@mipmap/weather_indicator"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_7day_weather"
                    app:layout_constraintRight_toRightOf="@+id/tv_7day_weather"
                    app:layout_constraintTop_toBottomOf="@+id/tv_7day_weather"/>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_24h_weather"
                    android:layout_width="@dimen/d880"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/d46"
                    android:fadingEdgeLength="@dimen/d100"
                    android:orientation="horizontal"
                    android:overScrollMode="never"
                    android:requiresFadingEdge="horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_24h_weather"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:ignore="MissingConstraints" />


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_7day_weather_list"
                    android:layout_width="@dimen/d880"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_marginStart="@dimen/d46"
                    android:fadingEdgeLength="@dimen/d100"
                    android:orientation="horizontal"
                    android:overScrollMode="never"
                    android:requiresFadingEdge="horizontal"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_24h_weather"
                    app:layout_constraintBottom_toBottomOf="parent"
                    tools:ignore="MissingConstraints" />


            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--指南-->
            <!--洗车-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_car_wash"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginLeft="32dp"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintLeft_toRightOf="@+id/cl_24h_weather"
                app:layout_constraintTop_toTopOf="@+id/cl_24h_weather"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_wash"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_car_wash"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_washcar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--紫外线-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_ultraviolet_rays"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginStart="@dimen/d32"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintBottom_toBottomOf="@+id/cl_car_wash"
                app:layout_constraintLeft_toRightOf="@+id/cl_car_wash"
                app:layout_constraintTop_toTopOf="@+id/cl_car_wash"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_uv"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_ultraviolet_rays"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_uv"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--感冒-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_common_cold"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginStart="@dimen/d32"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintBottom_toBottomOf="@+id/cl_ultraviolet_rays"
                app:layout_constraintLeft_toRightOf="@+id/cl_ultraviolet_rays"
                app:layout_constraintTop_toTopOf="@+id/cl_ultraviolet_rays"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_cold"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_common_cold"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_ganmao"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--运动-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_sport"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginTop="24dp"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintLeft_toLeftOf="@+id/cl_car_wash"
                app:layout_constraintRight_toRightOf="@+id/cl_car_wash"
                app:layout_constraintTop_toBottomOf="@+id/cl_car_wash"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_sport"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_sport"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_sport"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--穿衣-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_dress"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginStart="@dimen/d32"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintBottom_toBottomOf="@+id/cl_sport"
                app:layout_constraintLeft_toRightOf="@+id/cl_sport"
                app:layout_constraintTop_toTopOf="@+id/cl_sport"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_dress"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_dress"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_cloth"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!--旅游指数-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_travel_index"
                android:layout_width="@dimen/d220"
                android:layout_height="@dimen/d164"
                android:layout_marginStart="@dimen/d32"
                android:background="@drawable/air_quality_bg"
                app:layout_constraintBottom_toBottomOf="@+id/cl_dress"
                app:layout_constraintLeft_toRightOf="@+id/cl_dress"
                app:layout_constraintTop_toTopOf="@+id/cl_dress"
                tools:ignore="MissingConstraints">

                <TextView
                    style="@style/common_text_style_60"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginTop="@dimen/d30"
                    android:text="@string/def_cost_tourism"
                    android:textSize="@dimen/s24"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_travel_index"
                    style="@style/common_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="30dp"
                    android:layout_marginBottom="@dimen/d30"
                    android:text="@string/def_life_visibility_default"
                    android:textSize="@dimen/s28"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <ImageView
                    android:layout_width="@dimen/d36"
                    android:layout_height="@dimen/d36"
                    android:layout_marginTop="@dimen/d30"
                    android:layout_marginEnd="@dimen/d30"
                    android:src="@mipmap/ic_travel"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
            <!-- 备案号 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                app:layout_constraintTop_toBottomOf="@+id/cl_travel_index"
                app:layout_constraintRight_toRightOf="@+id/cl_travel_index"
                android:layout_marginTop="@dimen/d11"
                android:paddingRight="@dimen/d14"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_record_number"
                    android:layout_width="wrap_content"
                    app:layout_constraintRight_toLeftOf="@+id/iv_more"
                    app:layout_constraintTop_toTopOf="parent"
                    android:textSize="20sp"
                    android:gravity="center"
                    android:textColor="@color/record_number_text_color"
                    android:fontFamily="serif"
                    android:textFontWeight="500"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:text="@string/weather_record_number"
                    android:contentDescription="(@hide_uc(value=[true]))"
                    android:includeFontPadding="false"
                    android:layout_height="wrap_content" />

                <ImageView
                    android:id="@+id/iv_more"
                    android:layout_marginBottom="@dimen/d2"
                    android:layout_width="wrap_content"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_record_number"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_record_number"
                    android:background="@drawable/icon_more"
                    android:layout_height="wrap_content" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>