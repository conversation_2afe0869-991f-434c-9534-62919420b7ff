<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight">
        <!--将ActionBar隐藏,这里使用ToolBar-->
        <item name="windowActionBar">false</item>
        <!-- 使用 API Level 22以上编译的话，要拿掉前綴字 -->
        <item name="windowNoTitle">true</item>

        <item name="android:windowBackground">@mipmap/theme</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>


    <!-- Base application theme. -->

    <style name="common_text_style">
        <item name="android:textColor">@color/weather_text_color</item>
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="common_text_style_60">
        <item name="android:textColor">@color/weather_text_color_60</item>
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="common_text_style_70">
        <item name="android:textColor">@color/weather_text_color_70</item>
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="common_text_style_medium">
        <item name="android:textColor">@color/weather_text_color</item>
        <item name="fontFamily">serif</item>
        <item name="android:textFontWeight">600</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="hour_weather_text_style">
        <item name="android:textColor">@color/weather_text_color</item>
        <item name="fontFamily">sans-serif</item>
        <item name="android:textFontWeight">500</item>
        <item name="android:includeFontPadding">false</item>
    </style>
</resources>