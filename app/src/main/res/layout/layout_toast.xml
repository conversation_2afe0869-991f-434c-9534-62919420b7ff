<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_toast_blur"
        android:layout_width="464dp"
        android:layout_height="88dp"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/iv_toast_bg"
        android:layout_width="464dp"
        android:layout_height="88dp"
        android:layout_centerInParent="true"
        android:background="@drawable/toast_bg"
        android:scaleType="fitXY" />
    <!-- android:background="@mipmap/bg_toast"-->

    <TextView
        android:id="@+id/tv_toast"
        style="@style/common_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center"
        android:textColor="@color/theme_title_text_color"
        android:textSize="32px"
        android:contentDescription="(@hide_uc(value=[true]))" />
</RelativeLayout>
