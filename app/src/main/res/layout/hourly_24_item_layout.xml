<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/d120"
    android:layout_height="@dimen/d236"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_hourly_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:gravity="center"
        android:text="12:00"
        android:textColor="@color/weather_text_color"
        android:textSize="@dimen/s25"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_hourly_time_now"
        style="@style/common_text_style_medium"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:gravity="center"
        android:text="@string/def_now"
        android:textSize="@dimen/s25"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_hourly_weather"
        android:layout_width="@dimen/d72"
        android:layout_height="@dimen/d72"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/d35"
        android:src="@mipmap/ic_sunny"
        app:layout_constraintLeft_toLeftOf="@+id/tv_hourly_time"
        app:layout_constraintRight_toRightOf="@+id/tv_hourly_time"
        app:layout_constraintTop_toBottomOf="@+id/tv_hourly_time"
        app:layout_goneMarginTop="@dimen/d67" />

    <TextView
        android:id="@+id/tv_hourly_temp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/d3"
        android:contentDescription="(@hide_uc(value=[true]))"
        android:fontFamily="sans-serif-medium"
        android:text="24°C"
        android:textColor="@color/weather_text_color"
        android:textSize="@dimen/s28"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>