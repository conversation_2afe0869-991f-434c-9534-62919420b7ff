plugins {
    id 'com.android.application'
}

//自动构建apk
def DD_APK_BUILD_NUMBER
def DD_APK_VERSION
def DD_APK_NAME
def DD_APK_VERSION_CODE
def propFile = file('../build.number')
if (propFile.canRead()) {
    def props = new Properties()
    props.load(new FileInputStream(propFile))
    if (props != null && props.containsKey('DD_APK_VERSION')
            && props.containsKey('DD_APK_BUILD_NUMBER')) {
        DD_APK_BUILD_NUMBER = "${props['DD_APK_BUILD_NUMBER']}"
        DD_APK_VERSION = "${props['DD_APK_VERSION']}"
        DD_APK_NAME = "${props['DD_APK_NAME']}"
        DD_APK_VERSION_CODE = "${props['DD_APK_VERSION_CODE']}"
    }
}


android {
    compileSdk 34

    defaultConfig {
        applicationId "com.sgmw.lingos.weather"
        minSdk 30
        targetSdk 34
        versionCode "${DD_APK_VERSION_CODE}".toInteger()
        versionName "${DD_APK_VERSION}.${DD_APK_BUILD_NUMBER}"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }

        project.extensions.android.applicationVariants.all { variant ->
            def assembleTask = project.tasks.getByName("assemble${variant.name.capitalize()}")
            assembleTask.doLast { ->
                def srcPath = "${project.buildDir}${File.separator}outputs${File.separator}apk${File.separator}${variant.getFlavorName()}${File.separator}${variant.getBuildType().name}"
//        def srcPath = "${project.buildDir}${File.separator}outputs${File.separator}apk${File.separator}${variant.name}"
                println("srcPath==$srcPath")

                copy {
                    from(srcPath) {
                        include '*.apk'
                    }
                    into "${project.rootDir}${File.separator}out"
                    rename {
                        "${DD_APK_NAME}-${variant.buildType.name}.apk"
                    }
                }
            }
        }
    }



    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    dataBinding {
        enabled = true
    }

    signingConfigs {
        release {
            storeFile file("$rootDir/keystore/platform.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"

        }

        debug {
            storeFile file("$rootDir/keystore/platform.keystore")
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }

    namespace 'com.sgmw.lingos.weather'
    // 修改 APK 文件名称
    android.applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def appName = "SgmwWeather"
            def flavorName = variant.productFlavors[0].name
            if (flavorName == "_F510C") {
                outputFileName =  "${appName}_F510C.apk"
            } else if (flavorName == "_F710C") {
                outputFileName =  "${appName}_F710C.apk"
            }
        }

        def outputFilePath = "${rootDir.absolutePath}/apk/${variant.buildType.name}"
        File outputFile = new File(outputFilePath)

        variant.assembleProvider.get().doLast {
            copy {
                variant.outputs.all { file ->
                    copy {
                        from file.outputFile
                        into outputFile
                    }
                }
            }
        }
    }
    //多车型配置
    flavorDimensions "default"
    productFlavors {
        _F510C {
            dimension "default"
            buildConfigField("int", "car_model", '1')
        }
        _F710C {
            dimension "default"
            buildConfigField("int", "car_model", '2')
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src/main/java']
            jniLibs.srcDirs = ["libs"]
            res.srcDirs = ['src/main/res']
        }
        _F510C {
            res.srcDirs = ['src/main/res_f510c']
        }
        _F710C {
            res.srcDirs = ['src/main/res_f710c']
        }
    }

}



dependencies {
    //更新语音版本
    implementation 'com.sgmw.vrsdk:vrsdk:1.1.9-release@aar'

    _F510CApi 'com.sgmw.SGMWCommonWidget:SGMWCommonWidget:1.0.29@aar'
    _F710CApi  'com.sgmw.SGMWCommonWidget_F710S:SGMWCommonWidget_F710S:0.0.69.4-release'

    implementation 'com.sgmw.permissionclient:permissionclient:1.0.3@aar'
    implementation 'com.sgmw.SensorsAnalyticsSDK:SensorsAnalyticsSDK:1.0.0@aar'

    //替换新版本
    implementation 'com.sw.car:android.car:2.0.22-SNAPSHOT'

    //地图
    implementation 'com.sgmw.navisdk:navisdk:1.0.15.6-SNAPSHOT'

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.1.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    implementation 'com.jakewharton.timber:timber:4.7.1'
    // Gson
    implementation 'com.google.code.gson:gson:2.10'
    //okhttp 用于网络通信
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    // retrofit 用于网络请求错误处理等
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    // retrofit2:converter-gson 用于解析数据
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation project(':base-entity')
//    implementation project(':weather-card')

    //网络
    api 'com.blankj:utilcodex:1.31.1'
    api 'com.sgmw.avs.crypto:CryptoClient:*******-SNAPSHOT'

}

task copyApk(type: Copy) {
    description 'This is copy apk task'
    def apkDirF510C = file("${project.buildDir}/outputs/apk/_F510C")
    def apkDirF710C = file("${project.buildDir}/outputs/apk/_F710C")
    def apkDirAll = file("${project.buildDir}/outputs/apk/")
    def apkDirDebug = file("${project.buildDir}/outputs/apk/debug")
    def apkDirRelease = file("${project.buildDir}/outputs/apk/release")
    delete fileTree(dir: apkDirDebug, includes: ['**/*.apk'])
    delete fileTree(dir: apkDirRelease, includes: ['**/*.apk'])

    println "APK directory path: ${apkDirF510C.absolutePath}"
    from apkDirF510C, apkDirF710C
    into apkDirAll
    include "**/*.apk"
    // 在这里添加要执行的任务逻辑
    doLast {
        println 'Executing delete files task...'
        def f510cToDelete = file("${project.buildDir}/outputs/apk/_F510C")
        def f710cToDelete = file("${project.buildDir}/outputs/apk/_F710C")
        delete f510cToDelete
        delete f710cToDelete
    }
}

afterEvaluate {
    tasks.matching { it.name.startsWith('assemble') }.all { task ->
        // 为每个匹配的任务设置 finalizedBy
        task.finalizedBy(copyApk)
    }
}
