package com.sgmw.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;


public class Forecast implements Parcelable {
    private String moonset;//月落
    private String windSpeedDay;//白天风速
    private String sunrise;//日出
    private String windDegreesNight;//夜间的风向度数
    private String tempDay;//白天气温
    private String windLevelNight;//夜晚风力等级
    private String moonrise;//月出
    private String conditionDay;//白天天气
    private String moonphase;//月相
    private String sunset;//日落
    private String windLevelDay;//白天风力等级
    private String windSpeedNight;//晚上风速
    private String conditionIdNight;//晚上天气ID
    private String conditionNight;//晚上天气
    private String updatetime;//更新时间
    private String windDirDay;//白天风向
    private String windDegreesDay;//白天风向度数
    private String windDirNight;//晚上风向
    private String conditionIdDay;//晚上天气ID
    private String predictDate;//预测日期
    private String tempNight;//晚上温度


    public Forecast(String tempDay, String tempNight, String conditionDay, String predictDate) {
        this.tempDay = tempDay;
        this.conditionDay = conditionDay;
        this.predictDate = predictDate;
        this.tempNight = tempNight;
    }

    public String getMoonset() {
        return moonset;
    }

    public void setMoonset(String moonset) {
        this.moonset = moonset;
    }

    public String getWindSpeedDay() {
        return windSpeedDay;
    }

    public void setWindSpeedDay(String windSpeedDay) {
        this.windSpeedDay = windSpeedDay;
    }

    public String getSunrise() {
        return sunrise;
    }

    public void setSunrise(String sunrise) {
        this.sunrise = sunrise;
    }

    public String getWindDegreesNight() {
        return windDegreesNight;
    }

    public void setWindDegreesNight(String windDegreesNight) {
        this.windDegreesNight = windDegreesNight;
    }

    public String getTempDay() {
        return tempDay;
    }

    public void setTempDay(String tempDay) {
        this.tempDay = tempDay;
    }

    public String getWindLevelNight() {
        return windLevelNight;
    }

    public void setWindLevelNight(String windLevelNight) {
        this.windLevelNight = windLevelNight;
    }

    public String getMoonrise() {
        return moonrise;
    }

    public void setMoonrise(String moonrise) {
        this.moonrise = moonrise;
    }

    public String getConditionDay() {
        return conditionDay;
    }

    public void setConditionDay(String conditionDay) {
        this.conditionDay = conditionDay;
    }

    public String getMoonphase() {
        return moonphase;
    }

    public void setMoonphase(String moonphase) {
        this.moonphase = moonphase;
    }

    public String getSunset() {
        return sunset;
    }

    public void setSunset(String sunset) {
        this.sunset = sunset;
    }

    public String getWindLevelDay() {
        return windLevelDay;
    }

    public void setWindLevelDay(String windLevelDay) {
        this.windLevelDay = windLevelDay;
    }

    public String getWindSpeedNight() {
        return windSpeedNight;
    }

    public void setWindSpeedNight(String windSpeedNight) {
        this.windSpeedNight = windSpeedNight;
    }

    public String getConditionIdNight() {
        return conditionIdNight;
    }

    public void setConditionIdNight(String conditionIdNight) {
        this.conditionIdNight = conditionIdNight;
    }

    public String getConditionNight() {
        return conditionNight;
    }

    public void setConditionNight(String conditionNight) {
        this.conditionNight = conditionNight;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getWindDirDay() {
        return windDirDay;
    }

    public void setWindDirDay(String windDirDay) {
        this.windDirDay = windDirDay;
    }

    public String getWindDegreesDay() {
        return windDegreesDay;
    }

    public void setWindDegreesDay(String windDegreesDay) {
        this.windDegreesDay = windDegreesDay;
    }

    public String getWindDirNight() {
        return windDirNight;
    }

    public void setWindDirNight(String windDirNight) {
        this.windDirNight = windDirNight;
    }

    public String getConditionIdDay() {
        return conditionIdDay;
    }

    public void setConditionIdDay(String conditionIdDay) {
        this.conditionIdDay = conditionIdDay;
    }

    public String getPredictDate() {
        return predictDate;
    }

    public void setPredictDate(String predictDate) {
        this.predictDate = predictDate;
    }

    public String getTempNight() {
        return tempNight;
    }

    public void setTempNight(String tempNight) {
        this.tempNight = tempNight;
    }

    protected Forecast(Parcel in) {
        this.windSpeedDay = in.readString();
        this.sunrise = in.readString();
        this.windDegreesNight = in.readString();
        this.tempDay = in.readString();
        this.windLevelNight = in.readString();
        this.moonrise = in.readString();
        this.conditionDay = in.readString();
        this.moonphase = in.readString();
        this.sunset = in.readString();
        this.windLevelDay = in.readString();
        this.windSpeedNight = in.readString();
        this.conditionIdNight = in.readString();
        this.conditionNight = in.readString();
        this.updatetime = in.readString();
        this.windDirDay = in.readString();
        this.windDegreesDay = in.readString();
        this.windDirNight = in.readString();
        this.conditionIdDay = in.readString();
        this.predictDate = in.readString();
        this.tempNight = in.readString();
    }

    public static final Creator<Forecast> CREATOR = new Creator<Forecast>() {
        @Override
        public Forecast createFromParcel(Parcel in) {
            return new Forecast(in);
        }

        @Override
        public Forecast[] newArray(int size) {
            return new Forecast[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel parcel, int i) {
        parcel.writeString(this.windSpeedDay);
        parcel.writeString(this.sunrise);
        parcel.writeString(this.windDegreesNight);
        parcel.writeString(this.tempDay);
        parcel.writeString(this.windLevelNight);
        parcel.writeString(this.moonrise);
        parcel.writeString(this.conditionDay);
        parcel.writeString(this.moonphase);
        parcel.writeString(this.sunset);
        parcel.writeString(this.windLevelDay);
        parcel.writeString(this.windSpeedNight);
        parcel.writeString(this.conditionIdNight);
        parcel.writeString(this.conditionNight);
        parcel.writeString(this.updatetime);
        parcel.writeString(this.windDirDay);
        parcel.writeString(this.windDegreesDay);
        parcel.writeString(this.windDirNight);
        parcel.writeString(this.conditionIdDay);
        parcel.writeString(this.predictDate);
        parcel.writeString(this.tempNight);

    }

    public Forecast() {
    }

    @NonNull
    @Override
    public String toString() {
        return "Forecast{" +
                "windSpeedDay='" + windSpeedDay + '\'' +
                ", sunrise='" + sunrise + '\'' +
                ", windDegreesNight='" + windDegreesNight + '\'' +
                ", tempDay='" + tempDay + '\'' +
                ", windLevelNight='" + windLevelNight + '\'' +
                ", moonrise='" + moonrise + '\'' +
                ", conditionDay='" + conditionDay + '\'' +
                ", moonphase='" + moonphase + '\'' +
                ", sunset='" + sunset + '\'' +
                ", windLevelDay='" + windLevelDay + '\'' +
                ", windSpeedNight='" + windSpeedNight + '\'' +
                ", conditionIdNight='" + conditionIdNight + '\'' +
                ", conditionNight='" + conditionNight + '\'' +
                ", updatetime='" + updatetime + '\'' +
                ", windDirDay='" + windDirDay + '\'' +
                ", windDegreesDay='" + windDegreesDay + '\'' +
                ", windDirNight='" + windDirNight + '\'' +
                ", conditionIdDay='" + conditionIdDay + '\'' +
                ", predictDate='" + predictDate + '\'' +
                ", tempNight='" + tempNight + '\'' +
                '}';
    }


}
