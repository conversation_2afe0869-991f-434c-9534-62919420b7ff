package com.sgmw.entity;

public class HourlyDataItem {
    private String time;
    private String temp;
    private int weatherImageResId;
    private String weatherDesc;

    public HourlyDataItem(String time, String temp, int weatherImageResId, String weatherDesc) {
        this.time = time;
        this.temp = temp;
        this.weatherImageResId = weatherImageResId;
        this.weatherDesc = weatherDesc;
    }

    public void setWeatherImageResId(int weatherImageResId) {
        this.weatherImageResId = weatherImageResId;
    }

    public int getWeatherImageResId() {
        return weatherImageResId;
    }

    public void setTemp(String temp) {
        this.temp = temp;
    }

    public String getTemp() {
        return temp;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getTime() {
        return time;
    }

    public String getWeatherDesc() {
        return weatherDesc;
    }

    public void setWeatherDesc(String weatherDesc) {
        this.weatherDesc = weatherDesc;
    }

    @Override
    public String toString() {
        return "HourlyDataItem{" +
                "time='" + time + '\'' +
                ", temp='" + temp + '\'' +
                ", weatherImageResId=" + weatherImageResId +
                ", weatherDesc='" + weatherDesc + '\'' +
                '}';
    }
}
