package com.sgmw.entity;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

public class Hourly implements Parcelable {

    private String date;
    private String temp;
    private String qpf;
    private String uvi;
    private String pressure; //气压 单位 百帕
    private String windDir; //风向
    private String iconDay;
    private String pop;
    private String realFeel; //体感温度 单位摄氏度
    private String condition;
    private String windDegrees;
    private String hour;
    private String iconNight;
    private String snow;
    private String humidity; //湿度
    private String updatetime;
    private String windSpeed; //风速

    private boolean iconIsNight;

    public void setIconIsNight(boolean iconIsNight) {
        this.iconIsNight = iconIsNight;
    }

    public boolean isIconIsNight() {
        return iconIsNight;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getDate() {
        return date;
    }

    public void setTemp(String temp) {
        this.temp = temp;
    }

    public String getTemp() {
        return temp;
    }

    public void setQpf(String qpf) {
        this.qpf = qpf;
    }

    public String getQpf() {
        return qpf;
    }

    public void setUvi(String uvi) {
        this.uvi = uvi;
    }

    public String getUvi() {
        return uvi;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getPressure() {
        return pressure;
    }

    public void setWindDir(String windDir) {
        this.windDir = windDir;
    }

    public String getWindDir() {
        return windDir;
    }

    public void setIconDay(String iconDay) {
        this.iconDay = iconDay;
    }

    public String getIconDay() {
        return iconDay;
    }

    public void setPop(String pop) {
        this.pop = pop;
    }

    public String getPop() {
        return pop;
    }

    public void setRealFeel(String realFeel) {
        this.realFeel = realFeel;
    }

    public String getRealFeel() {
        return realFeel;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getCondition() {
        return condition;
    }

    public void setWindDegrees(String windDegrees) {
        this.windDegrees = windDegrees;
    }

    public String getWindDegrees() {
        return windDegrees;
    }

    public void setHour(String hour) {
        this.hour = hour;
    }

    public String getHour() {
        return hour;
    }

    public void setIconNight(String iconNight) {
        this.iconNight = iconNight;
    }

    public String getIconNight() {
        return iconNight;
    }

    public void setSnow(String snow) {
        this.snow = snow;
    }

    public String getSnow() {
        return snow;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setWindSpeed(String windSpeed) {
        this.windSpeed = windSpeed;
    }

    public String getWindSpeed() {
        return windSpeed;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.date);
        dest.writeString(this.temp);
        dest.writeString(this.qpf);
        dest.writeString(this.uvi);
        dest.writeString(this.pressure);
        dest.writeString(this.windDir);
        dest.writeString(this.iconDay);
        dest.writeString(this.pop);
        dest.writeString(this.realFeel);
        dest.writeString(this.condition);
        dest.writeString(this.windDegrees);
        dest.writeString(this.hour);
        dest.writeString(this.iconNight);
        dest.writeString(this.snow);
        dest.writeString(this.humidity);
        dest.writeString(this.updatetime);
        dest.writeString(this.windSpeed);
    }

    public void readFromParcel(Parcel source) {
        this.date = source.readString();
        this.temp = source.readString();
        this.qpf = source.readString();
        this.uvi = source.readString();
        this.pressure = source.readString();
        this.windDir = source.readString();
        this.iconDay = source.readString();
        this.pop = source.readString();
        this.realFeel = source.readString();
        this.condition = source.readString();
        this.windDegrees = source.readString();
        this.hour = source.readString();
        this.iconNight = source.readString();
        this.snow = source.readString();
        this.humidity = source.readString();
        this.updatetime = source.readString();
        this.windSpeed = source.readString();
    }

    /*
     *测试数据
     * "condition":"晴","date":"2024-01-10","hour":"15","humidity":"39","iconDay":"0","iconNight":"30",
     *"pop":"0","pressure":"1014","qpf":"0","realFeel":"-2","snow":"0","temp":"3","updatetime":"2024-01-10 15:48:15",
     *"uvi":"1","windDegrees":"225","windDir":"SSW","windSpeed":"16.2"
     * */
    public Hourly() {
        this.date = "2024-01-10";
        this.temp = "8";
        this.qpf = "0";
        this.uvi = "1";
        this.pressure = "1014";
        this.windDir = "SSW";
        this.iconDay = "0";
        this.pop = "0";
        this.realFeel = "-2";
        this.condition = "晴";
        this.windDegrees = "225";
        this.hour = "15";
        this.iconNight = "30";
        this.snow = "0";
        this.humidity = "39";
        this.updatetime = "2024-01-10 15:48:15";
        this.windSpeed = "16.2";
    }

    protected Hourly(Parcel in) {
        this.date = in.readString();
        this.temp = in.readString();
        this.qpf = in.readString();
        this.uvi = in.readString();
        this.pressure = in.readString();
        this.windDir = in.readString();
        this.iconDay = in.readString();
        this.pop = in.readString();
        this.realFeel = in.readString();
        this.condition = in.readString();
        this.windDegrees = in.readString();
        this.hour = in.readString();
        this.iconNight = in.readString();
        this.snow = in.readString();
        this.humidity = in.readString();
        this.updatetime = in.readString();
        this.windSpeed = in.readString();
    }

    public static final Creator<Hourly> CREATOR = new Creator<Hourly>() {
        @Override
        public Hourly createFromParcel(Parcel source) {
            return new Hourly(source);
        }

        @Override
        public Hourly[] newArray(int size) {
            return new Hourly[size];
        }
    };

    @NonNull
    @Override
    public String toString() {
        return "Hourly{" +
                "date='" + date + '\'' +
                ", temp='" + temp + '\'' +
                ", qpf='" + qpf + '\'' +
                ", uvi='" + uvi + '\'' +
                ", pressure='" + pressure + '\'' +
                ", windDir='" + windDir + '\'' +
                ", iconDay='" + iconDay + '\'' +
                ", pop='" + pop + '\'' +
                ", realFeel='" + realFeel + '\'' +
                ", condition='" + condition + '\'' +
                ", windDegrees='" + windDegrees + '\'' +
                ", hour='" + hour + '\'' +
                ", iconNight='" + iconNight + '\'' +
                ", snow='" + snow + '\'' +
                ", humidity='" + humidity + '\'' +
                ", updatetime='" + updatetime + '\'' +
                ", windSpeed='" + windSpeed + '\'' +
                '}';
    }
}
