package com.sgmw.entity;

import android.os.Parcel;
import android.os.Parcelable;

public class LiveIndex implements Parcelable {

    private int code;
    private String level;
    private String name;
    private String updatetime;
    private String day;
    private String desc;
    private String status;

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevel() {
        return level;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setUpdatetime(String updatetime) {
        this.updatetime = updatetime;
    }

    public String getUpdatetime() {
        return updatetime;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getDay() {
        return day;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.code);
        dest.writeString(this.level);
        dest.writeString(this.name);
        dest.writeString(this.updatetime);
        dest.writeString(this.day);
        dest.writeString(this.desc);
        dest.writeString(this.status);
    }

    public void readFromParcel(Parcel source) {
        this.code = source.readInt();
        this.level = source.readString();
        this.name = source.readString();
        this.updatetime = source.readString();
        this.day = source.readString();
        this.desc = source.readString();
        this.status = source.readString();
    }

    /*假数据用于测试*/
    public LiveIndex() {
        this.code = 26;
        this.level = "11";
        this.name = "运动指数";
        this.updatetime = "2024-01-10 14:24:03";
        this.day = "2024-01-10";
        this.desc = "气温过低，特别容易着凉感冒，较不适宜户外运动，建议室内运动。";
        this.status = "不适宜";
    }
    protected LiveIndex(Parcel in) {
        this.code = in.readInt();
        this.level = in.readString();
        this.name = in.readString();
        this.updatetime = in.readString();
        this.day = in.readString();
        this.desc = in.readString();
        this.status = in.readString();
    }

    public static final Creator<LiveIndex> CREATOR = new Creator<LiveIndex>() {
        @Override
        public LiveIndex createFromParcel(Parcel source) {
            return new LiveIndex(source);
        }

        @Override
        public LiveIndex[] newArray(int size) {
            return new LiveIndex[size];
        }
    };
}
