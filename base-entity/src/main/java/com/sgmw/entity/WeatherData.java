package com.sgmw.entity;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class WeatherData implements Parcelable {
    private String code;//实时天气现象代码
    private String address;//地址
    private String visibility;//实时能见度，单位为公里
    private String districtName;//地区名称
    private String provinceName;//省名
    private String cityNameStr;//市名
    private List<LiveIndex> liveIndex;//生活指数列表
    private String temperature;//实时温度
    private List<Hourly> hourly;//24小时列表
    private String text;//实时天气现象文字
    private String tempDay;//白天气温(最高)
    private String tempNight;//晚上气温(最低)
    private String aqi;//空气质量
    private String aqiValue;//空气质量指数值
    private String tips = "";//一句话提示
    private Long currentTime;//当前时间
    private List<Forecast> forecast;//未来8天天气列表
    private int sunrise = 0;//日出
    private long sunUp = 0;//日出时间戳
    private long sunDown = 0;//日落时间戳
    private int sunset = 0;//日落


    public List<Forecast> getForecast() {
        return forecast;
    }

    public void setForecast(List<Forecast> forecast) {
        this.forecast = forecast;
    }

    public Long getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Long currentTime) {
        this.currentTime = currentTime;
    }

    public String getAqi() {
        return aqi;
    }

    public void setAqi(String aqi) {
        this.aqi = aqi;
    }

    public String getAqiValue() {
        return aqiValue;
    }

    public void setAqiValue(String aqiValue) {
        this.aqiValue = aqiValue;
    }

    public String getTempDay() {
        return tempDay;
    }

    public void setTempDay(String tempDay) {
        this.tempDay = tempDay;
    }

    public String getTempNight() {
        return tempNight;
    }

    public void setTempNight(String tempNight) {
        this.tempNight = tempNight;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }


    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setVisibility(String visibility) {
        this.visibility = visibility;
    }

    public String getVisibility() {
        return visibility;
    }

    public void setDistrictName(String districtName) {
        this.districtName = districtName;
    }

    public String getDistrictName() {
        return districtName;
    }

    public String getProvinceName() {
        return provinceName;
    }

    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    public String getCityNameStr() {
        return cityNameStr;
    }

    public void setCityNameStr(String cityNameStr) {
        this.cityNameStr = cityNameStr;
    }

    public void setLiveIndex(List<LiveIndex> liveIndex) {
        this.liveIndex = liveIndex;
    }

    public List<LiveIndex> getLiveIndex() {
        return liveIndex;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setHourly(List<Hourly> hourly) {
        this.hourly = hourly;
    }

    public List<Hourly> getHourly() {
        return hourly;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getText() {
        return text;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public int getSunrise() {
        return sunrise;
    }

    public void setSunrise(int sunrise) {
        this.sunrise = sunrise;
    }

    public int getSunset() {
        return sunset;
    }

    public void setSunset(int sunset) {
        this.sunset = sunset;
    }

    public long getSunUp() {
        return sunUp;
    }

    public void setSunUp(long sunUp) {
        this.sunUp = sunUp;
    }

    public long getSunDown() {
        return sunDown;
    }

    public void setSunDown(long sunDown) {
        this.sunDown = sunDown;
    }

    @Override
    public String toString() {
        return "WeatherData{" +
                "code='" + code + '\'' +
                ", address='" + address + '\'' +
                ", visibility='" + visibility + '\'' +
                ", districtName='" + districtName + '\'' +
                ", provinceName='" + provinceName + '\'' +
                ", cityNameStr='" + cityNameStr + '\'' +
                ", liveIndex=" + liveIndex +
                ", temperature='" + temperature + '\'' +
                ", hourly=" + hourly +
                ", text='" + text + '\'' +
                ", tempDay='" + tempDay + '\'' +
                ", tempNight='" + tempNight + '\'' +
                ", aqi='" + aqi + '\'' +
                ", tips='" + tips + '\'' +
                ", currentTime=" + currentTime +
                ", forecast=" + forecast +
                ", sunrise=" + sunrise +
                ", sunUp=" + sunUp +
                ", sunDown=" + sunDown +
                ", sunset=" + sunset +
                '}';
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.code);
        dest.writeString(this.address);
        dest.writeString(this.visibility);
        dest.writeString(this.districtName);
        dest.writeList(this.liveIndex);
        dest.writeString(this.temperature);
        dest.writeList(this.hourly);
        dest.writeString(this.text);
        dest.writeString(this.tempDay);
        dest.writeString(this.tempNight);
        dest.writeString(this.aqi);
        dest.writeString(this.tips);
        dest.writeLong(this.currentTime);
        dest.writeList(this.forecast);
        dest.writeInt(this.sunrise);
        dest.writeInt(this.sunset);
        dest.writeLong(this.sunUp);
        dest.writeLong(this.sunDown);
    }

    public void readFromParcel(Parcel source) {
        this.code = source.readString();
        this.address = source.readString();
        this.visibility = source.readString();
        this.districtName = source.readString();
        this.liveIndex = new ArrayList<LiveIndex>();
        source.readList(this.liveIndex, LiveIndex.class.getClassLoader());
        this.temperature = source.readString();
        this.hourly = new ArrayList<Hourly>();
        source.readList(this.hourly, Hourly.class.getClassLoader());
        this.text = source.readString();
        this.tempDay = source.readString();
        this.tempNight = source.readString();
        this.aqi = source.readString();
        this.tips = source.readString();
        this.currentTime = source.readLong();
        this.forecast = new ArrayList<Forecast>();
        source.readList(this.forecast, Forecast.class.getClassLoader());
        this.sunrise = source.readInt();
        this.sunset = source.readInt();
        this.sunUp = source.readLong();
        this.sunDown = source.readLong();
    }

    public WeatherData() {
    }

    protected WeatherData(Parcel in) {
        this.code = in.readString();
        this.address = in.readString();
        this.visibility = in.readString();
        this.districtName = in.readString();
        this.liveIndex = new ArrayList<LiveIndex>();
        in.readList(this.liveIndex, LiveIndex.class.getClassLoader());
        this.temperature = in.readString();
        this.hourly = new ArrayList<Hourly>();
        in.readList(this.hourly, Hourly.class.getClassLoader());
        this.text = in.readString();
        this.tempDay = in.readString();
        this.tempNight = in.readString();
        this.aqi = in.readString();
        this.tips = in.readString();
        this.currentTime = in.readLong();
        this.forecast = new ArrayList<Forecast>();
        in.readList(this.forecast, Forecast.class.getClassLoader());
        this.sunrise = in.readInt();
        this.sunset = in.readInt();
        this.sunUp = in.readLong();
        this.sunDown = in.readLong();
    }

    public static final Creator<WeatherData> CREATOR = new Creator<WeatherData>() {
        @Override
        public WeatherData createFromParcel(Parcel source) {
            return new WeatherData(source);
        }

        @Override
        public WeatherData[] newArray(int size) {
            return new WeatherData[size];
        }
    };
}
